function defineDriver(e){return e}function normalizeKey(e,n=":"){return e?e.replace(/[:/\\]/g,n).replace(/^[:/\\]|[:/\\]$/g,""):""}function joinKeys(...e){return e.map((e=>normalizeKey(e))).filter(Boolean).join(":")}function createError(e,n,i){const t=new Error(`[unstorage] [${e}] ${n}`,i);return Error.captureStackTrace&&Error.captureStackTrace(t,createError),t}function getBinding(e){let n="[binding]";if("string"==typeof e&&(n=e,e=globalThis[n]||globalThis.__env__?.[n]),!e)throw createError("cloudflare",`Invalid binding \`${n}\`: \`${e}\``);for(const i of["get","put","delete"])if(!(i in e))throw createError("cloudflare",`Invalid binding \`${n}\`: \`${i}\` key is missing`);return e}function getKVBinding(e="STORAGE"){return getBinding(e)}globalThis.process??={},globalThis.process.env??={};const DRIVER_NAME="cloudflare-kv-binding",cloudflareKvBinding=defineDriver((e=>{const n=(n="")=>e.base?joinKeys(e.base,n):n;async function i(i=""){i=n(i);const t=getKVBinding(e.binding),r=[];let o;do{const e=await t.list({prefix:i||void 0,cursor:o});r.push(...e.keys),o=e.list_complete?void 0:e.cursor}while(o);return r.map((e=>e.name))}return{name:DRIVER_NAME,options:e,getInstance:()=>getKVBinding(e.binding),async hasItem(i){i=n(i);const t=getKVBinding(e.binding);return null!==await t.get(i)},getItem(i){i=n(i);return getKVBinding(e.binding).get(i)},setItem(i,t,r){i=n(i);return getKVBinding(e.binding).put(i,t,r?{expirationTtl:r?.ttl?Math.max(r.ttl,e.minTTL??60):void 0,...r}:void 0)},removeItem(i){i=n(i);return getKVBinding(e.binding).delete(i)},getKeys:n=>i(n).then((n=>n.map((n=>e.base?n.slice(e.base.length):n)))),async clear(n){const t=getKVBinding(e.binding),r=await i(n);await Promise.all(r.map((e=>t.delete(e))))}}}));export{cloudflareKvBinding as default};