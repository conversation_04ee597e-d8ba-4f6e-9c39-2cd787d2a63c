function getOptimizedImageUrl(t,e={}){if(t.startsWith("/")||t.includes("placeholder"))return t;const{width:i,height:r,quality:a=85,format:s="auto",fit:o="scale-down",sharpen:n,blur:h,saturation:p,brightness:u,contrast:m,gamma:g}=e,c=[];i&&c.push(`width=${i}`),r&&c.push(`height=${r}`),a&&c.push(`quality=${a}`),s&&c.push(`format=${s}`),o&&c.push(`fit=${o}`),n&&c.push(`sharpen=${n}`),h&&c.push(`blur=${h}`),p&&c.push(`saturation=${p}`),u&&c.push(`brightness=${u}`),m&&c.push(`contrast=${m}`),g&&c.push(`gamma=${g}`);return`/cdn-cgi/image/${c.join(",")}/${t}`}function getResponsiveImageUrls(t,e={}){const{sizes:i=[320,640,960,1280,1920],densities:r=[1,2],...a}=e,s=getOptimizedImageUrl(t,{...a,width:Math.max(...i)}),o=[];for(const e of i)for(const i of r){const r=e*i,s=getOptimizedImageUrl(t,{...a,width:r});o.push(`${s} ${r}w`)}return{src:s,srcset:o.join(", ")}}globalThis.process??={},globalThis.process.env??={};const ImagePresets={productCard:t=>getOptimizedImageUrl(t,{width:800,height:600,quality:85,format:"auto",fit:"cover"}),productDetail:t=>getOptimizedImageUrl(t,{width:1200,height:900,quality:90,format:"auto",fit:"contain"}),thumbnail:t=>getOptimizedImageUrl(t,{width:150,height:150,quality:80,format:"auto",fit:"cover"}),hero:t=>getOptimizedImageUrl(t,{width:1920,height:1080,quality:90,format:"auto",fit:"cover"}),related:t=>getOptimizedImageUrl(t,{width:600,height:450,quality:85,format:"auto",fit:"cover"})};function generateSizesAttribute(t={}){const e={"(max-width: 640px)":"100vw","(max-width: 1024px)":"50vw","(max-width: 1280px)":"33vw",...t},i=Object.entries(e),r=i.slice(0,-1).map((([t,e])=>`${t} ${e}`)),a=i[i.length-1][1];return r.push(a),r.join(", ")}function supportsAVIF(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),r=t.match(/Version\/(\d+).*Safari/);return!!(e&&parseInt(e[1])>=85)||(!!(i&&parseInt(i[1])>=93)||!!(r&&parseInt(r[1])>=16))}function supportsWebP(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),r=t.match(/Version\/(\d+).*Safari/),a=t.match(/Edge\/(\d+)/);return!!(e&&parseInt(e[1])>=23)||(!!(i&&parseInt(i[1])>=65)||(!!(r&&parseInt(r[1])>=14)||!!(a&&parseInt(a[1])>=18)))}function getOptimalFormat(t){return supportsAVIF(t)?"avif":supportsWebP(t)?"webp":"jpeg"}export{ImagePresets,generateSizesAttribute,getOptimalFormat,getOptimizedImageUrl,getResponsiveImageUrls,supportsAVIF,supportsWebP};