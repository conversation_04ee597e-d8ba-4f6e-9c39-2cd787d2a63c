[["Map", 1, 2], "meta::meta", ["Map", 3, 4, 5, 6], "astro-version", "5.12.4", "astro-config-digest", "{\"root\":{},\"srcDir\":{},\"publicDir\":{},\"outDir\":{},\"cacheDir\":{},\"site\":\"https://infpik.store\",\"compressHTML\":true,\"base\":\"/\",\"trailingSlash\":\"ignore\",\"output\":\"server\",\"scopedStyleStrategy\":\"attribute\",\"build\":{\"format\":\"directory\",\"client\":{},\"server\":{},\"assets\":\"_astro\",\"serverEntry\":\"index.js\",\"redirects\":false,\"inlineStylesheets\":\"auto\",\"concurrency\":1},\"server\":{\"open\":false,\"host\":false,\"port\":4321,\"streaming\":true,\"allowedHosts\":[]},\"redirects\":{},\"image\":{\"endpoint\":{\"route\":\"/_image\"},\"service\":{\"entrypoint\":\"@astrojs/cloudflare/image-service\",\"config\":{}},\"domains\":[],\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"**.amazonaws.com\"},{\"protocol\":\"https\",\"hostname\":\"polar.sh\"},{\"protocol\":\"https\",\"hostname\":\"**.polar.sh\"},{\"protocol\":\"https\",\"hostname\":\"**.amazonaws.com\"},{\"protocol\":\"https\",\"hostname\":\"polar.sh\"},{\"protocol\":\"https\",\"hostname\":\"**.polar.sh\"}],\"responsiveStyles\":false},\"devToolbar\":{\"enabled\":true},\"markdown\":{\"syntaxHighlight\":{\"type\":\"shiki\",\"excludeLangs\":[\"math\"]},\"shikiConfig\":{\"langs\":[],\"langAlias\":{},\"theme\":\"github-dark\",\"themes\":{},\"wrap\":false,\"transformers\":[]},\"remarkPlugins\":[],\"rehypePlugins\":[],\"remarkRehype\":{},\"gfm\":true,\"smartypants\":true},\"security\":{\"checkOrigin\":true},\"env\":{\"schema\":{},\"validateSecrets\":false},\"experimental\":{\"clientPrerender\":false,\"contentIntellisense\":false,\"headingIdCompat\":false,\"preserveScriptOrder\":false,\"liveContentCollections\":false,\"csp\":false,\"rawEnvValues\":false},\"legacy\":{\"collections\":false},\"session\":{\"driver\":\"cloudflare-kv-binding\",\"options\":{\"binding\":\"SESSION\"}}}"]