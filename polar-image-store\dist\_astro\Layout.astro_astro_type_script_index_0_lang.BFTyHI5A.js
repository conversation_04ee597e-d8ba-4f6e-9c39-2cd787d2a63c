document.addEventListener("DOMContentLoaded",()=>{const c=document.getElementById("mobile-menu-button"),y=document.getElementById("mobile-menu");c&&y&&c.addEventListener("click",()=>{y.classList.toggle("hidden")});const l=document.getElementById("headerSearchContainer"),d=document.getElementById("mobileHeaderSearchContainer");let h=!1;function g(){const e=window.scrollY;l&&(e<=50?(l.classList.add("opacity-0","-translate-y-2"),l.classList.remove("opacity-100","translate-y-0")):(l.classList.remove("opacity-0","-translate-y-2"),l.classList.add("opacity-100","translate-y-0"))),d&&(e<=50?(d.classList.add("opacity-0","-translate-y-2"),d.classList.remove("opacity-100","translate-y-0")):(d.classList.remove("opacity-0","-translate-y-2"),d.classList.add("opacity-100","translate-y-0"))),h=!1}function b(){h||(requestAnimationFrame(g),h=!0)}window.addEventListener("scroll",b,{passive:!0}),g();const a=document.getElementById("productSearch"),u=document.getElementById("mobileProductSearch"),s=document.getElementById("searchResults");let f;function m(){return window.innerWidth<768}function v(e){if(m()){e.blur();const t=e.value.trim();window.openSearchModal?.(t)}}function x(e){try{const i=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(n=>n!==e);i.unshift(e);const r=i.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(r))}catch(t){console.error("Failed to save recent search:",t)}}function p(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&s){const t=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((r,n)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${n<e.length-1?"border-b border-primary-100":""}">
                  <button class="recent-search-item flex-1 text-left" data-query="${r}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${r}</span>
                    </div>
                  </button>
                  <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${r}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>
          `;s.innerHTML=t,s.classList.remove("hidden"),s.querySelectorAll(".recent-search-item").forEach(r=>{r.addEventListener("click",()=>{const n=r.getAttribute("data-query");n&&a&&(a.value=n,k(n))})}),s.querySelectorAll(".delete-recent-search").forEach(r=>{r.addEventListener("click",n=>{n.stopPropagation();const o=r.getAttribute("data-query");o&&(w(o),p())})}),s.querySelector("#clearRecentSearches")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),s?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}function w(e){try{const i=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(r=>r!==e);localStorage.setItem("recentSearches",JSON.stringify(i))}catch(t){console.error("Failed to remove recent search:",t)}}function k(e){x(e),S({value:e})}async function S(e){const t=e.value.trim();f&&clearTimeout(f),t.length>2?s&&(s.classList.remove("hidden"),s.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching All images for "${t}"...</span>
              </div>
            </div>
          `,f=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(t)}`)).json();if(s&&!s.classList.contains("hidden"))if(r.results&&r.results.length>0){const n=r.results.map(o=>`
                    <button onclick="window.location.href='${o.url}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${o.displayName}</div>
                            <div class="text-primary-600 text-sm">${o.count} ${o.count===1?"product":"products"}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join("");s.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Search All images for "${t}"
                      </div>
                      ${n}
                      ${r.total>r.results.length?`
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${r.results.length} of ${r.total} tags
                          </div>
                        </div>
                      `:""}
                    </div>
                  `}else s.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No images found for "${t}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all images →
                      </a>
                    </div>
                  `}catch(i){console.error("Tag search error:",i),s&&!s.classList.contains("hidden")&&(s.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):p()}a&&(a.addEventListener("focus",e=>{v(e.target)}),a.addEventListener("click",e=>{v(e.target)}),a.addEventListener("input",e=>{m()||S(e.target)}),a.addEventListener("focus",e=>{!m()&&!e.target.value.trim()&&p()}),a.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&(m()?window.openSearchModal?.(t):(x(t),window.location.href=`/products?search=${encodeURIComponent(t)}`))}})),u&&(u.addEventListener("focus",e=>{v(e.target)}),u.addEventListener("click",e=>{v(e.target)}),u.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&window.openSearchModal?.(t)}})),document.addEventListener("click",e=>{s&&!a?.contains(e.target)&&!s.contains(e.target)&&s.classList.add("hidden")})});"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(c=>{console.log("SW registered: ",c)}).catch(c=>{console.log("SW registration failed: ",c)})});
