globalThis.process??={},globalThis.process.env??={};import{j as joinPaths,i as isRemotePath}from"./path_h5kZAkfu.mjs";import{A as AstroError,ag as ExpectedImage,ah as LocalImageUsedWrongly,ai as MissingImageDimension,aj as UnsupportedImageFormat,ak as IncompatibleDescriptorOptions,al as UnsupportedImageConversion,am as toStyleString,an as NoImageMetadata,ao as FailedToFetchRemoteImageDimensions,ap as ExpectedImageOptions,aq as ExpectedNotESMImage,ar as InvalidImageService,c as createAstro,a as createComponent,as as ImageMissingAlt,m as maybeRenderHead,b as addAttribute,s as spreadAttributes,d as renderTemplate,at as ExperimentalFontsNotEnabled,au as FontFamilyNotFound,u as unescapeHTML}from"./astro/server_BgKLHZ62.mjs";const VALID_SUPPORTED_FORMATS=["jpeg","jpg","png","tiff","webp","gif","svg","avif"],DEFAULT_OUTPUT_FORMAT="webp",DEFAULT_HASH_PROPS=["src","width","height","format","quality","fit","position"],DEFAULT_RESOLUTIONS=[640,750,828,960,1080,1280,1668,1920,2048,2560,3200,3840,4480,5120,6016],LIMITED_RESOLUTIONS=[640,750,828,1080,1280,1668,2048,2560],getWidths=({width:t,layout:e,breakpoints:i=DEFAULT_RESOLUTIONS,originalWidth:a})=>{const o=t=>!a||t<=a;if("full-width"===e)return i.filter(o);if(!t)return[];const s=2*t,n=a?Math.min(s,a):s;return"fixed"===e?a&&t>a?[a]:[t,n]:"constrained"===e?[t,s,...i].filter((t=>t<=n)).sort(((t,e)=>t-e)):[]},getSizesAttribute=({width:t,layout:e})=>{if(t&&e)switch(e){case"constrained":return`(min-width: ${t}px) ${t}px, 100vw`;case"fixed":return`${t}px`;case"full-width":return"100vw";default:return}};function matchPattern(t,e){return matchProtocol(t,e.protocol)&&matchHostname(t,e.hostname,!0)&&matchPort(t,e.port)&&matchPathname(t,e.pathname,!0)}function matchPort(t,e){return!e||e===t.port}function matchProtocol(t,e){return!e||e===t.protocol.slice(0,-1)}function matchHostname(t,e,i=!1){if(!e)return!0;if(!i||!e.startsWith("*"))return e===t.hostname;if(e.startsWith("**.")){const i=e.slice(2);return i!==t.hostname&&t.hostname.endsWith(i)}if(e.startsWith("*.")){const i=e.slice(1);return 1===t.hostname.replace(i,"").split(".").filter(Boolean).length}return!1}function matchPathname(t,e,i=!1){if(!e)return!0;if(!i||!e.endsWith("*"))return e===t.pathname;if(e.endsWith("/**")){const i=e.slice(0,-2);return i!==t.pathname&&t.pathname.startsWith(i)}if(e.endsWith("/*")){const i=e.slice(0,-1);return 1===t.pathname.replace(i,"").split("/").filter(Boolean).length}return!1}function isRemoteAllowed(t,{domains:e,remotePatterns:i}){if(!URL.canParse(t))return!1;const a=new URL(t);return e.some((t=>matchHostname(a,t)))||i.some((t=>matchPattern(a,t)))}function isESMImportedImage(t){return"object"==typeof t||"function"==typeof t&&"src"in t}function isRemoteImage(t){return"string"==typeof t}async function resolveSrc(t){if("object"==typeof t&&"then"in t){const e=await t;return e.default??e}return t}function isLocalService(t){return!!t&&"transform"in t}const sortNumeric=(t,e)=>t-e,baseService={propertiesToHash:DEFAULT_HASH_PROPS,validateOptions(t){if(!t.src||!isRemoteImage(t.src)&&!isESMImportedImage(t.src))throw new AstroError({...ExpectedImage,message:ExpectedImage.message(JSON.stringify(t.src),typeof t.src,JSON.stringify(t,((t,e)=>void 0===e?null:e)))});if(isESMImportedImage(t.src)){if(!VALID_SUPPORTED_FORMATS.includes(t.src.format))throw new AstroError({...UnsupportedImageFormat,message:UnsupportedImageFormat.message(t.src.format,t.src.src,VALID_SUPPORTED_FORMATS)});if(t.widths&&t.densities)throw new AstroError(IncompatibleDescriptorOptions);if("svg"===t.src.format&&(t.format="svg"),"svg"===t.src.format&&"svg"!==t.format||"svg"!==t.src.format&&"svg"===t.format)throw new AstroError(UnsupportedImageConversion)}else{if(t.src.startsWith("/@fs/")||!isRemotePath(t.src)&&!t.src.startsWith("/"))throw new AstroError({...LocalImageUsedWrongly,message:LocalImageUsedWrongly.message(t.src)});let e;if(t.width||t.height?!t.width&&t.height?e="width":t.width&&!t.height&&(e="height"):e="both",e)throw new AstroError({...MissingImageDimension,message:MissingImageDimension.message(e,t.src)})}return t.format||(t.format="webp"),t.width&&(t.width=Math.round(t.width)),t.height&&(t.height=Math.round(t.height)),t.layout&&t.width&&t.height&&(t.fit??="cover",delete t.layout),"none"===t.fit&&delete t.fit,t},getHTMLAttributes(t){const{targetWidth:e,targetHeight:i}=getTargetDimensions(t),{src:a,width:o,height:s,format:n,quality:r,densities:p,widths:c,formats:l,layout:m,priority:d,fit:g,position:h,...f}=t;return{...f,width:e,height:i,loading:f.loading??"lazy",decoding:f.decoding??"async"}},getSrcSet(t){const{targetWidth:e,targetHeight:i}=getTargetDimensions(t),a=e/i,{widths:o,densities:s}=t,n=t.format??"webp";let r=(o??[]).sort(sortNumeric),p=t.width,c=1/0;isESMImportedImage(t.src)&&(p=t.src.width,c=p,r.length>0&&r.at(-1)>c&&(r=r.filter((t=>t<=c)),r.push(c))),r=Array.from(new Set(r));const{width:l,height:m,...d}=t;let g=[];if(s){const t=s.map((t=>"number"==typeof t?t:parseFloat(t)));g=t.sort(sortNumeric).map((t=>Math.round(e*t))).map(((e,i)=>({width:e,descriptor:`${t[i]}x`})))}else r.length>0&&(g=r.map((t=>({width:t,descriptor:`${t}w`}))));return g.map((({width:t,descriptor:e})=>{const i=Math.round(t/a);return{transform:{...d,width:t,height:i},descriptor:e,attributes:{type:`image/${n}`}}}))},getURL(t,e){const i=new URLSearchParams;if(isESMImportedImage(t.src))i.append("href",t.src.src);else{if(!isRemoteAllowed(t.src,e))return t.src;i.append("href",t.src)}Object.entries({w:"width",h:"height",q:"quality",f:"format",fit:"fit",position:"position"}).forEach((([e,a])=>{t[a]&&i.append(e,t[a].toString())}));return`${joinPaths("/",e.endpoint.route)}?${i}`},parseURL(t){const e=t.searchParams;if(!e.has("href"))return;return{src:e.get("href"),width:e.has("w")?parseInt(e.get("w")):void 0,height:e.has("h")?parseInt(e.get("h")):void 0,format:e.get("f"),quality:e.get("q"),fit:e.get("fit"),position:e.get("position")??void 0}}};function getTargetDimensions(t){let e=t.width,i=t.height;if(isESMImportedImage(t.src)){const a=t.src.width/t.src.height;i&&!e?e=Math.round(i*a):e&&!i?i=Math.round(e/a):e||i||(e=t.src.width,i=t.src.height)}return{targetWidth:e,targetHeight:i}}function isImageMetadata(t){return t.fsPath&&!("fsPath"in t)}const cssFitValues=["fill","contain","cover","scale-down"];function addCSSVarsToStyle(t,e){const i=Object.entries(t).filter((([t,e])=>void 0!==e&&!1!==e)).map((([t,e])=>`--${t}: ${e};`)).join(" ");if(!e)return i;return`${i} ${"string"==typeof e?e:toStyleString(e)}`}const decoder=new TextDecoder,toUTF8String=(t,e=0,i=t.length)=>decoder.decode(t.slice(e,i)),toHexString=(t,e=0,i=t.length)=>t.slice(e,i).reduce(((t,e)=>t+("0"+e.toString(16)).slice(-2)),""),readInt16LE=(t,e=0)=>{const i=t[e]+256*t[e+1];return i|131070*(32768&i)},readUInt16BE=(t,e=0)=>256*t[e]+t[e+1],readUInt16LE=(t,e=0)=>t[e]+256*t[e+1],readUInt24LE=(t,e=0)=>t[e]+256*t[e+1]+65536*t[e+2],readInt32LE=(t,e=0)=>t[e]+256*t[e+1]+65536*t[e+2]+(t[e+3]<<24),readUInt32BE=(t,e=0)=>t[e]*2**24+65536*t[e+1]+256*t[e+2]+t[e+3],readUInt32LE=(t,e=0)=>t[e]+256*t[e+1]+65536*t[e+2]+t[e+3]*2**24,methods={readUInt16BE:readUInt16BE,readUInt16LE:readUInt16LE,readUInt32BE:readUInt32BE,readUInt32LE:readUInt32LE};function readUInt(t,e,i,a){i=i||0;return methods["readUInt"+e+(a?"BE":"LE")](t,i)}function readBox(t,e){if(t.length-e<4)return;const i=readUInt32BE(t,e);return t.length-e<i?void 0:{name:toUTF8String(t,4+e,8+e),offset:e,size:i}}function findBox(t,e,i){for(;i<t.length;){const a=readBox(t,i);if(!a)break;if(a.name===e)return a;i+=a.size}}const BMP={validate:t=>"BM"===toUTF8String(t,0,2),calculate:t=>({height:Math.abs(readInt32LE(t,22)),width:readUInt32LE(t,18)})},TYPE_ICON=1,SIZE_HEADER$1=6,SIZE_IMAGE_ENTRY=16;function getSizeFromOffset(t,e){const i=t[e];return 0===i?256:i}function getImageSize$1(t,e){const i=6+16*e;return{height:getSizeFromOffset(t,i+1),width:getSizeFromOffset(t,i)}}const ICO={validate(t){const e=readUInt16LE(t,0),i=readUInt16LE(t,4);if(0!==e||0===i)return!1;return 1===readUInt16LE(t,2)},calculate(t){const e=readUInt16LE(t,4),i=getImageSize$1(t,0);if(1===e)return i;const a=[i];for(let i=1;i<e;i+=1)a.push(getImageSize$1(t,i));return{height:i.height,images:a,width:i.width}}},TYPE_CURSOR=2,CUR={validate(t){const e=readUInt16LE(t,0),i=readUInt16LE(t,4);if(0!==e||0===i)return!1;return 2===readUInt16LE(t,2)},calculate:t=>ICO.calculate(t)},DDS={validate:t=>542327876===readUInt32LE(t,0),calculate:t=>({height:readUInt32LE(t,12),width:readUInt32LE(t,16)})},gifRegexp=/^GIF8[79]a/,GIF={validate:t=>gifRegexp.test(toUTF8String(t,0,6)),calculate:t=>({height:readUInt16LE(t,8),width:readUInt16LE(t,6)})},brandMap={avif:"avif",avis:"avif",mif1:"heif",msf1:"heif",heic:"heic",heix:"heic",hevc:"heic",hevx:"heic"};function detectBrands(t,e,i){let a={};for(let o=e;o<=i;o+=4){const e=toUTF8String(t,o,o+4);e in brandMap&&(a[e]=1)}return"avif"in a||"avis"in a?"avif":"heic"in a||"heix"in a||"hevc"in a||"hevx"in a?"heic":"mif1"in a||"msf1"in a?"heif":void 0}const HEIF={validate(t){const e=toUTF8String(t,4,8),i=toUTF8String(t,8,12);return"ftyp"===e&&i in brandMap},calculate(t){const e=findBox(t,"meta",0),i=e&&findBox(t,"iprp",e.offset+12),a=i&&findBox(t,"ipco",i.offset+8),o=a&&findBox(t,"ispe",a.offset+8);if(o)return{height:readUInt32BE(t,o.offset+16),width:readUInt32BE(t,o.offset+12),type:detectBrands(t,8,e.offset)};throw new TypeError("Invalid HEIF, no size found")}},SIZE_HEADER=8,FILE_LENGTH_OFFSET=4,ENTRY_LENGTH_OFFSET=4,ICON_TYPE_SIZE={ICON:32,"ICN#":32,"icm#":16,icm4:16,icm8:16,"ics#":16,ics4:16,ics8:16,is32:16,s8mk:16,icp4:16,icl4:32,icl8:32,il32:32,l8mk:32,icp5:32,ic11:32,ich4:48,ich8:48,ih32:48,h8mk:48,icp6:64,ic12:32,it32:128,t8mk:128,ic07:128,ic08:256,ic13:256,ic09:512,ic14:512,ic10:1024};function readImageHeader(t,e){const i=e+4;return[toUTF8String(t,e,i),readUInt32BE(t,i)]}function getImageSize(t){const e=ICON_TYPE_SIZE[t];return{width:e,height:e,type:t}}const ICNS={validate:t=>"icns"===toUTF8String(t,0,4),calculate(t){const e=t.length,i=readUInt32BE(t,4);let a=8,o=readImageHeader(t,a),s=getImageSize(o[0]);if(a+=o[1],a===i)return s;const n={height:s.height,images:[s],width:s.width};for(;a<i&&a<e;)o=readImageHeader(t,a),s=getImageSize(o[0]),a+=o[1],n.images.push(s);return n}},J2C={validate:t=>"ff4fff51"===toHexString(t,0,4),calculate:t=>({height:readUInt32BE(t,12),width:readUInt32BE(t,8)})},JP2={validate(t){if(1783636e3!==readUInt32BE(t,4)||readUInt32BE(t,0)<1)return!1;const e=findBox(t,"ftyp",0);return!!e&&1718909296===readUInt32BE(t,e.offset+4)},calculate(t){const e=findBox(t,"jp2h",0),i=e&&findBox(t,"ihdr",e.offset+8);if(i)return{height:readUInt32BE(t,i.offset+8),width:readUInt32BE(t,i.offset+12)};throw new TypeError("Unsupported JPEG 2000 format")}},EXIF_MARKER="45786966",APP1_DATA_SIZE_BYTES=2,EXIF_HEADER_BYTES=6,TIFF_BYTE_ALIGN_BYTES=2,BIG_ENDIAN_BYTE_ALIGN="4d4d",LITTLE_ENDIAN_BYTE_ALIGN="4949",IDF_ENTRY_BYTES=12,NUM_DIRECTORY_ENTRIES_BYTES=2;function isEXIF(t){return"45786966"===toHexString(t,2,6)}function extractSize(t,e){return{height:readUInt16BE(t,e),width:readUInt16BE(t,e+2)}}function extractOrientation(t,e){const i=readUInt(t,16,14,e);for(let a=0;a<i;a++){const i=16+12*a,o=i+12;if(i>t.length)return;const s=t.slice(i,o);if(274===readUInt(s,16,0,e)){if(3!==readUInt(s,16,2,e))return;if(1!==readUInt(s,32,4,e))return;return readUInt(s,16,8,e)}}}function validateExifBlock(t,e){const i=t.slice(2,e),a=toHexString(i,6,8),o="4d4d"===a;if(o||"4949"===a)return extractOrientation(i,o)}function validateInput(t,e){if(e>t.length)throw new TypeError("Corrupt JPG, exceeded buffer limits")}const JPG={validate:t=>"ffd8"===toHexString(t,0,2),calculate(t){let e,i;for(t=t.slice(4);t.length;){const a=readUInt16BE(t,0);if(255===t[a]){if(isEXIF(t)&&(e=validateExifBlock(t,a)),validateInput(t,a),i=t[a+1],192===i||193===i||194===i){const i=extractSize(t,a+5);return e?{height:i.height,orientation:e,width:i.width}:i}t=t.slice(a+2)}else t=t.slice(a)}throw new TypeError("Invalid JPG, no size found")}},KTX={validate:t=>{const e=toUTF8String(t,1,7);return["KTX 11","KTX 20"].includes(e)},calculate:t=>{const e=49===t[5]?"ktx":"ktx2",i="ktx"===e?36:20;return{height:readUInt32LE(t,i+4),width:readUInt32LE(t,i),type:e}}},pngSignature="PNG\r\n\n",pngImageHeaderChunkName="IHDR",pngFriedChunkName="CgBI",PNG={validate(t){if("PNG\r\n\n"===toUTF8String(t,1,8)){let e=toUTF8String(t,12,16);if("CgBI"===e&&(e=toUTF8String(t,28,32)),"IHDR"!==e)throw new TypeError("Invalid PNG");return!0}return!1},calculate:t=>"CgBI"===toUTF8String(t,12,16)?{height:readUInt32BE(t,36),width:readUInt32BE(t,32)}:{height:readUInt32BE(t,20),width:readUInt32BE(t,16)}},PNMTypes={P1:"pbm/ascii",P2:"pgm/ascii",P3:"ppm/ascii",P4:"pbm",P5:"pgm",P6:"ppm",P7:"pam",PF:"pfm"},handlers={default:t=>{let e=[];for(;t.length>0;){const i=t.shift();if("#"!==i[0]){e=i.split(" ");break}}if(2===e.length)return{height:parseInt(e[1],10),width:parseInt(e[0],10)};throw new TypeError("Invalid PNM")},pam:t=>{const e={};for(;t.length>0;){const i=t.shift();if(i.length>16||i.charCodeAt(0)>128)continue;const[a,o]=i.split(" ");if(a&&o&&(e[a.toLowerCase()]=parseInt(o,10)),e.height&&e.width)break}if(e.height&&e.width)return{height:e.height,width:e.width};throw new TypeError("Invalid PAM")}},PNM={validate:t=>toUTF8String(t,0,2)in PNMTypes,calculate(t){const e=toUTF8String(t,0,2),i=PNMTypes[e],a=toUTF8String(t,3).split(/[\r\n]+/);return(handlers[i]||handlers.default)(a)}},PSD={validate:t=>"8BPS"===toUTF8String(t,0,4),calculate:t=>({height:readUInt32BE(t,14),width:readUInt32BE(t,18)})},svgReg=/<svg\s([^>"']|"[^"]*"|'[^']*')*>/,extractorRegExps={height:/\sheight=(['"])([^%]+?)\1/,root:svgReg,viewbox:/\sviewBox=(['"])(.+?)\1/i,width:/\swidth=(['"])([^%]+?)\1/},INCH_CM=2.54,units={in:96,cm:96/2.54,em:16,ex:8,m:96/2.54*100,mm:96/2.54/10,pc:96/72/12,pt:96/72,px:1},unitsReg=new RegExp(`^([0-9.]+(?:e\\d+)?)(${Object.keys(units).join("|")})?$`);function parseLength(t){const e=unitsReg.exec(t);if(e)return Math.round(Number(e[1])*(units[e[2]]||1))}function parseViewbox(t){const e=t.split(" ");return{height:parseLength(e[3]),width:parseLength(e[2])}}function parseAttributes(t){const e=extractorRegExps.width.exec(t),i=extractorRegExps.height.exec(t),a=extractorRegExps.viewbox.exec(t);return{height:i&&parseLength(i[2]),viewbox:a&&parseViewbox(a[2]),width:e&&parseLength(e[2])}}function calculateByDimensions(t){return{height:t.height,width:t.width}}function calculateByViewbox(t,e){const i=e.width/e.height;return t.width?{height:Math.floor(t.width/i),width:t.width}:t.height?{height:t.height,width:Math.floor(t.height*i)}:{height:e.height,width:e.width}}const SVG={validate:t=>svgReg.test(toUTF8String(t,0,1e3)),calculate(t){const e=extractorRegExps.root.exec(toUTF8String(t));if(e){const t=parseAttributes(e[0]);if(t.width&&t.height)return calculateByDimensions(t);if(t.viewbox)return calculateByViewbox(t,t.viewbox)}throw new TypeError("Invalid SVG")}},TGA={validate:t=>0===readUInt16LE(t,0)&&0===readUInt16LE(t,4),calculate:t=>({height:readUInt16LE(t,14),width:readUInt16LE(t,12)})};function readIFD(t,e){const i=readUInt(t,32,4,e);return t.slice(i+2)}function readValue(t,e){const i=readUInt(t,16,8,e);return(readUInt(t,16,10,e)<<16)+i}function nextTag(t){if(t.length>24)return t.slice(12)}function extractTags(t,e){const i={};let a=t;for(;a&&a.length;){const t=readUInt(a,16,0,e),o=readUInt(a,16,2,e),s=readUInt(a,32,4,e);if(0===t)break;1!==s||3!==o&&4!==o||(i[t]=readValue(a,e)),a=nextTag(a)}return i}function determineEndianness(t){const e=toUTF8String(t,0,2);return"II"===e?"LE":"MM"===e?"BE":void 0}const signatures=["49492a00","4d4d002a"],TIFF={validate:t=>signatures.includes(toHexString(t,0,4)),calculate(t){const e="BE"===determineEndianness(t),i=extractTags(readIFD(t,e),e),a=i[256],o=i[257];if(!a||!o)throw new TypeError("Invalid Tiff. Missing tags");return{height:o,width:a}}};function calculateExtended(t){return{height:1+readUInt24LE(t,7),width:1+readUInt24LE(t,4)}}function calculateLossless(t){return{height:1+((15&t[4])<<10|t[3]<<2|(192&t[2])>>6),width:1+((63&t[2])<<8|t[1])}}function calculateLossy(t){return{height:16383&readInt16LE(t,8),width:16383&readInt16LE(t,6)}}const WEBP={validate(t){const e="RIFF"===toUTF8String(t,0,4),i="WEBP"===toUTF8String(t,8,12),a="VP8"===toUTF8String(t,12,15);return e&&i&&a},calculate(t){const e=toUTF8String(t,12,16);if(t=t.slice(20,30),"VP8X"===e){const e=t[0];if(!(192&e)&&!(1&e))return calculateExtended(t);throw new TypeError("Invalid WebP")}if("VP8 "===e&&47!==t[0])return calculateLossy(t);const i=toHexString(t,3,6);if("VP8L"===e&&"9d012a"!==i)return calculateLossless(t);throw new TypeError("Invalid WebP")}},typeHandlers=new Map([["bmp",BMP],["cur",CUR],["dds",DDS],["gif",GIF],["heif",HEIF],["icns",ICNS],["ico",ICO],["j2c",J2C],["jp2",JP2],["jpg",JPG],["ktx",KTX],["png",PNG],["pnm",PNM],["psd",PSD],["svg",SVG],["tga",TGA],["tiff",TIFF],["webp",WEBP]]),types=Array.from(typeHandlers.keys()),firstBytes=new Map([[56,"psd"],[66,"bmp"],[68,"dds"],[71,"gif"],[73,"tiff"],[77,"tiff"],[82,"webp"],[105,"icns"],[137,"png"],[255,"jpg"]]);function detector(t){const e=t[0],i=firstBytes.get(e);return i&&typeHandlers.get(i).validate(t)?i:types.find((e=>typeHandlers.get(e).validate(t)))}function lookup$1(t){const e=detector(t);if(void 0!==e){const i=typeHandlers.get(e).calculate(t);if(void 0!==i)return i.type=i.type??e,i}throw new TypeError("unsupported file type: "+e)}async function imageMetadata(t,e){let i;try{i=lookup$1(t)}catch{throw new AstroError({...NoImageMetadata,message:NoImageMetadata.message(e)})}if(!i.height||!i.width||!i.type)throw new AstroError({...NoImageMetadata,message:NoImageMetadata.message(e)});const{width:a,height:o,type:s,orientation:n}=i,r=(n||0)>=5;return{width:r?o:a,height:r?a:o,format:s,orientation:n}}async function inferRemoteSize(t){const e=await fetch(t);if(!e.body||!e.ok)throw new AstroError({...FailedToFetchRemoteImageDimensions,message:FailedToFetchRemoteImageDimensions.message(t)});const i=e.body.getReader();let a,o,s=new Uint8Array;for(;!a;){const e=await i.read();if(a=e.done,a)break;if(e.value){o=e.value;let a=new Uint8Array(s.length+o.length);a.set(s,0),a.set(o,s.length),s=a;try{const e=await imageMetadata(s,t);if(e)return await i.cancel(),e}catch{}}}throw new AstroError({...NoImageMetadata,message:NoImageMetadata.message(t)})}async function getConfiguredImageService(){if(!globalThis?.astroAsset?.imageService){const{default:t}=await import("./image-service_Db1gBhpI.mjs").catch((t=>{const e=new AstroError(InvalidImageService);throw e.cause=t,e}));return globalThis.astroAsset||(globalThis.astroAsset={}),globalThis.astroAsset.imageService=t,t}return globalThis.astroAsset.imageService}async function getImage$1(t,e){if(!t||"object"!=typeof t)throw new AstroError({...ExpectedImageOptions,message:ExpectedImageOptions.message(JSON.stringify(t))});if(void 0===t.src)throw new AstroError({...ExpectedImage,message:ExpectedImage.message(t.src,"undefined",JSON.stringify(t))});if(isImageMetadata(t))throw new AstroError(ExpectedNotESMImage);const i=await getConfiguredImageService(),a={...t,src:await resolveSrc(t.src)};let o,s;if(t.inferSize&&isRemoteImage(a.src)&&isRemotePath(a.src)){const t=await inferRemoteSize(a.src);a.width??=t.width,a.height??=t.height,o=t.width,s=t.height,delete a.inferSize}const n=isESMImportedImage(a.src)?a.src.fsPath:void 0,r=isESMImportedImage(a.src)?a.src.clone??a.src:a.src;if(isESMImportedImage(r)&&(o=r.width,s=r.height),o&&s){const t=o/s;a.height&&!a.width?a.width=Math.round(a.height*t):a.width&&!a.height?a.height=Math.round(a.width/t):a.width||a.height||(a.width=o,a.height=s)}a.src=r;const p=t.layout??e.layout??"none";a.priority?(a.loading??="eager",a.decoding??="sync",a.fetchpriority??="high",delete a.priority):(a.loading??="lazy",a.decoding??="async",a.fetchpriority??="auto"),"none"!==p&&(a.widths||=getWidths({width:a.width,layout:p,originalWidth:o,breakpoints:e.breakpoints?.length?e.breakpoints:isLocalService(i)?LIMITED_RESOLUTIONS:DEFAULT_RESOLUTIONS}),a.sizes||=getSizesAttribute({width:a.width,layout:p}),delete a.densities,a.style=addCSSVarsToStyle({fit:cssFitValues.includes(a.fit??"")&&a.fit,pos:a.position},a.style),a["data-astro-image"]=p);const c=i.validateOptions?await i.validateOptions(a,e):a,l=i.getSrcSet?await i.getSrcSet(c,e):[];let m=await i.getURL(c,e);const d=t=>t.width===c.width&&t.height===c.height&&t.format===c.format;let g=await Promise.all(l.map((async t=>({transform:t.transform,url:d(t.transform)?m:await i.getURL(t.transform,e),descriptor:t.descriptor,attributes:t.attributes}))));if(isLocalService(i)&&globalThis.astroAsset.addStaticImage&&(!isRemoteImage(c.src)||m!==c.src)){const t=i.propertiesToHash??DEFAULT_HASH_PROPS;m=globalThis.astroAsset.addStaticImage(c,t,n),g=l.map((e=>({transform:e.transform,url:d(e.transform)?m:globalThis.astroAsset.addStaticImage(e.transform,t,n),descriptor:e.descriptor,attributes:e.attributes})))}return{rawOptions:a,options:c,src:m,srcSet:{values:g,attribute:g.map((t=>`${t.url} ${t.descriptor}`)).join(", ")},attributes:void 0!==i.getHTMLAttributes?await i.getHTMLAttributes(c,e):{}}}const $$Astro$2=createAstro("https://infpik.store"),$$Image=createComponent((async(t,e,i)=>{const a=t.createAstro($$Astro$2,e,i);a.self=$$Image;const o=a.props;if(void 0===o.alt||null===o.alt)throw new AstroError(ImageMissingAlt);"string"==typeof o.width&&(o.width=parseInt(o.width)),"string"==typeof o.height&&(o.height=parseInt(o.height));"none"!==(o.layout??imageConfig.layout??"none")&&(o.layout??=imageConfig.layout,o.fit??=imageConfig.objectFit??"cover",o.position??=imageConfig.objectPosition??"center");const s=await getImage(o),n={};s.srcSet.values.length>0&&(n.srcset=s.srcSet.attribute);const{class:r,...p}={...n,...s.attributes};return renderTemplate`${maybeRenderHead()}<img${addAttribute(s.src,"src")}${spreadAttributes(p)}${addAttribute(r,"class")}>`}),"D:/code/image/polar-image-store/node_modules/astro/components/Image.astro",void 0),mimes={"3g2":"video/3gpp2","3gp":"video/3gpp","3gpp":"video/3gpp","3mf":"model/3mf",aac:"audio/aac",ac:"application/pkix-attr-cert",adp:"audio/adpcm",adts:"audio/aac",ai:"application/postscript",aml:"application/automationml-aml+xml",amlx:"application/automationml-amlx+zip",amr:"audio/amr",apng:"image/apng",appcache:"text/cache-manifest",appinstaller:"application/appinstaller",appx:"application/appx",appxbundle:"application/appxbundle",asc:"application/pgp-keys",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomdeleted:"application/atomdeleted+xml",atomsvc:"application/atomsvc+xml",au:"audio/basic",avci:"image/avci",avcs:"image/avcs",avif:"image/avif",aw:"application/applixware",bdoc:"application/bdoc",bin:"application/octet-stream",bmp:"image/bmp",bpk:"application/octet-stream",btf:"image/prs.btif",btif:"image/prs.btif",buffer:"application/octet-stream",ccxml:"application/ccxml+xml",cdfx:"application/cdfx+xml",cdmia:"application/cdmi-capability",cdmic:"application/cdmi-container",cdmid:"application/cdmi-domain",cdmio:"application/cdmi-object",cdmiq:"application/cdmi-queue",cer:"application/pkix-cert",cgm:"image/cgm",cjs:"application/node",class:"application/java-vm",coffee:"text/coffeescript",conf:"text/plain",cpl:"application/cpl+xml",cpt:"application/mac-compactpro",crl:"application/pkix-crl",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",cwl:"application/cwl",cww:"application/prs.cww",davmount:"application/davmount+xml",dbk:"application/docbook+xml",deb:"application/octet-stream",def:"text/plain",deploy:"application/octet-stream",dib:"image/bmp","disposition-notification":"message/disposition-notification",dist:"application/octet-stream",distz:"application/octet-stream",dll:"application/octet-stream",dmg:"application/octet-stream",dms:"application/octet-stream",doc:"application/msword",dot:"application/msword",dpx:"image/dpx",drle:"image/dicom-rle",dsc:"text/prs.lines.tag",dssc:"application/dssc+der",dtd:"application/xml-dtd",dump:"application/octet-stream",dwd:"application/atsc-dwd+xml",ear:"application/java-archive",ecma:"application/ecmascript",elc:"application/octet-stream",emf:"image/emf",eml:"message/rfc822",emma:"application/emma+xml",emotionml:"application/emotionml+xml",eps:"application/postscript",epub:"application/epub+zip",exe:"application/octet-stream",exi:"application/exi",exp:"application/express",exr:"image/aces",ez:"application/andrew-inset",fdf:"application/fdf",fdt:"application/fdt+xml",fits:"image/fits",g3:"image/g3fax",gbr:"application/rpki-ghostbusters",geojson:"application/geo+json",gif:"image/gif",glb:"model/gltf-binary",gltf:"model/gltf+json",gml:"application/gml+xml",gpx:"application/gpx+xml",gram:"application/srgs",grxml:"application/srgs+xml",gxf:"application/gxf",gz:"application/gzip",h261:"video/h261",h263:"video/h263",h264:"video/h264",heic:"image/heic",heics:"image/heic-sequence",heif:"image/heif",heifs:"image/heif-sequence",hej2:"image/hej2k",held:"application/atsc-held+xml",hjson:"application/hjson",hlp:"application/winhlp",hqx:"application/mac-binhex40",hsj2:"image/hsj2",htm:"text/html",html:"text/html",ics:"text/calendar",ief:"image/ief",ifb:"text/calendar",iges:"model/iges",igs:"model/iges",img:"application/octet-stream",in:"text/plain",ini:"text/plain",ink:"application/inkml+xml",inkml:"application/inkml+xml",ipfix:"application/ipfix",iso:"application/octet-stream",its:"application/its+xml",jade:"text/jade",jar:"application/java-archive",jhc:"image/jphc",jls:"image/jls",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpgm:"image/jpm",jpgv:"video/jpeg",jph:"image/jph",jpm:"image/jpm",jpx:"image/jpx",js:"text/javascript",json:"application/json",json5:"application/json5",jsonld:"application/ld+json",jsonml:"application/jsonml+json",jsx:"text/jsx",jt:"model/jt",jxl:"image/jxl",jxr:"image/jxr",jxra:"image/jxra",jxrs:"image/jxrs",jxs:"image/jxs",jxsc:"image/jxsc",jxsi:"image/jxsi",jxss:"image/jxss",kar:"audio/midi",ktx:"image/ktx",ktx2:"image/ktx2",less:"text/less",lgr:"application/lgr+xml",list:"text/plain",litcoffee:"text/coffeescript",log:"text/plain",lostxml:"application/lost+xml",lrf:"application/octet-stream",m1v:"video/mpeg",m21:"application/mp21",m2a:"audio/mpeg",m2t:"video/mp2t",m2ts:"video/mp2t",m2v:"video/mpeg",m3a:"audio/mpeg",m4a:"audio/mp4",m4p:"application/mp4",m4s:"video/iso.segment",ma:"application/mathematica",mads:"application/mads+xml",maei:"application/mmt-aei+xml",man:"text/troff",manifest:"text/cache-manifest",map:"application/json",mar:"application/octet-stream",markdown:"text/markdown",mathml:"application/mathml+xml",mb:"application/mathematica",mbox:"application/mbox",md:"text/markdown",mdx:"text/mdx",me:"text/troff",mesh:"model/mesh",meta4:"application/metalink4+xml",metalink:"application/metalink+xml",mets:"application/mets+xml",mft:"application/rpki-manifest",mid:"audio/midi",midi:"audio/midi",mime:"message/rfc822",mj2:"video/mj2",mjp2:"video/mj2",mjs:"text/javascript",mml:"text/mathml",mods:"application/mods+xml",mov:"video/quicktime",mp2:"audio/mpeg",mp21:"application/mp21",mp2a:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4s:"application/mp4",mp4v:"video/mp4",mpd:"application/dash+xml",mpe:"video/mpeg",mpeg:"video/mpeg",mpf:"application/media-policy-dataset+xml",mpg:"video/mpeg",mpg4:"video/mp4",mpga:"audio/mpeg",mpp:"application/dash-patch+xml",mrc:"application/marc",mrcx:"application/marcxml+xml",ms:"text/troff",mscml:"application/mediaservercontrol+xml",msh:"model/mesh",msi:"application/octet-stream",msix:"application/msix",msixbundle:"application/msixbundle",msm:"application/octet-stream",msp:"application/octet-stream",mtl:"model/mtl",mts:"video/mp2t",musd:"application/mmt-usd+xml",mxf:"application/mxf",mxmf:"audio/mobile-xmf",mxml:"application/xv+xml",n3:"text/n3",nb:"application/mathematica",nq:"application/n-quads",nt:"application/n-triples",obj:"model/obj",oda:"application/oda",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",omdoc:"application/omdoc+xml",onepkg:"application/onenote",onetmp:"application/onenote",onetoc:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",otf:"font/otf",owl:"application/rdf+xml",oxps:"application/oxps",p10:"application/pkcs10",p7c:"application/pkcs7-mime",p7m:"application/pkcs7-mime",p7s:"application/pkcs7-signature",p8:"application/pkcs8",pdf:"application/pdf",pfr:"application/font-tdpfr",pgp:"application/pgp-encrypted",pkg:"application/octet-stream",pki:"application/pkixcmp",pkipath:"application/pkix-pkipath",pls:"application/pls+xml",png:"image/png",prc:"model/prc",prf:"application/pics-rules",provx:"application/provenance+xml",ps:"application/postscript",pskcxml:"application/pskc+xml",pti:"image/prs.pti",qt:"video/quicktime",raml:"application/raml+yaml",rapd:"application/route-apd+xml",rdf:"application/rdf+xml",relo:"application/p2p-overlay+xml",rif:"application/reginfo+xml",rl:"application/resource-lists+xml",rld:"application/resource-lists-diff+xml",rmi:"audio/midi",rnc:"application/relax-ng-compact-syntax",rng:"application/xml",roa:"application/rpki-roa",roff:"text/troff",rq:"application/sparql-query",rs:"application/rls-services+xml",rsat:"application/atsc-rsat+xml",rsd:"application/rsd+xml",rsheet:"application/urc-ressheet+xml",rss:"application/rss+xml",rtf:"text/rtf",rtx:"text/richtext",rusd:"application/route-usd+xml",s3m:"audio/s3m",sbml:"application/sbml+xml",scq:"application/scvp-cv-request",scs:"application/scvp-cv-response",sdp:"application/sdp",senmlx:"application/senml+xml",sensmlx:"application/sensml+xml",ser:"application/java-serialized-object",setpay:"application/set-payment-initiation",setreg:"application/set-registration-initiation",sgi:"image/sgi",sgm:"text/sgml",sgml:"text/sgml",shex:"text/shex",shf:"application/shf+xml",shtml:"text/html",sieve:"application/sieve",sig:"application/pgp-signature",sil:"audio/silk",silo:"model/mesh",siv:"application/sieve",slim:"text/slim",slm:"text/slim",sls:"application/route-s-tsid+xml",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",so:"application/octet-stream",spdx:"text/spdx",spp:"application/scvp-vp-response",spq:"application/scvp-vp-request",spx:"audio/ogg",sql:"application/sql",sru:"application/sru+xml",srx:"application/sparql-results+xml",ssdl:"application/ssdl+xml",ssml:"application/ssml+xml",stk:"application/hyperstudio",stl:"model/stl",stpx:"model/step+xml",stpxz:"model/step-xml+zip",stpz:"model/step+zip",styl:"text/stylus",stylus:"text/stylus",svg:"image/svg+xml",svgz:"image/svg+xml",swidtag:"application/swid+xml",t:"text/troff",t38:"image/t38",td:"application/urc-targetdesc+xml",tei:"application/tei+xml",teicorpus:"application/tei+xml",text:"text/plain",tfi:"application/thraud+xml",tfx:"image/tiff-fx",tif:"image/tiff",tiff:"image/tiff",toml:"application/toml",tr:"text/troff",trig:"application/trig",ts:"video/mp2t",tsd:"application/timestamped-data",tsv:"text/tab-separated-values",ttc:"font/collection",ttf:"font/ttf",ttl:"text/turtle",ttml:"application/ttml+xml",txt:"text/plain",u3d:"model/u3d",u8dsn:"message/global-delivery-status",u8hdr:"message/global-headers",u8mdn:"message/global-disposition-notification",u8msg:"message/global",ubj:"application/ubjson",uri:"text/uri-list",uris:"text/uri-list",urls:"text/uri-list",vcard:"text/vcard",vrml:"model/vrml",vtt:"text/vtt",vxml:"application/voicexml+xml",war:"application/java-archive",wasm:"application/wasm",wav:"audio/wav",weba:"audio/webm",webm:"video/webm",webmanifest:"application/manifest+json",webp:"image/webp",wgsl:"text/wgsl",wgt:"application/widget",wif:"application/watcherinfo+xml",wmf:"image/wmf",woff:"font/woff",woff2:"font/woff2",wrl:"model/vrml",wsdl:"application/wsdl+xml",wspolicy:"application/wspolicy+xml",x3d:"model/x3d+xml",x3db:"model/x3d+fastinfoset",x3dbz:"model/x3d+binary",x3dv:"model/x3d-vrml",x3dvz:"model/x3d+vrml",x3dz:"model/x3d+xml",xaml:"application/xaml+xml",xav:"application/xcap-att+xml",xca:"application/xcap-caps+xml",xcs:"application/calendar+xml",xdf:"application/xcap-diff+xml",xdssc:"application/dssc+xml",xel:"application/xcap-el+xml",xenc:"application/xenc+xml",xer:"application/patch-ops-error+xml",xfdf:"application/xfdf",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xhvml:"application/xv+xml",xlf:"application/xliff+xml",xm:"audio/xm",xml:"text/xml",xns:"application/xcap-ns+xml",xop:"application/xop+xml",xpl:"application/xproc+xml",xsd:"application/xml",xsf:"application/prs.xsf+xml",xsl:"application/xml",xslt:"application/xml",xspf:"application/xspf+xml",xvm:"application/xv+xml",xvml:"application/xv+xml",yaml:"text/yaml",yang:"application/yang",yin:"application/yin+xml",yml:"text/yaml",zip:"application/zip"};function lookup(t){let e=(""+t).trim().toLowerCase(),i=e.lastIndexOf(".");return mimes[~i?e.substring(++i):e]}const $$Astro$1=createAstro("https://infpik.store"),$$Picture=createComponent((async(t,e,i)=>{const a=t.createAstro($$Astro$1,e,i);a.self=$$Picture;const o=["webp"],{formats:s=o,pictureAttributes:n={},fallbackFormat:r,...p}=a.props;if(void 0===p.alt||null===p.alt)throw new AstroError(ImageMissingAlt);const c=p.class?.match(/\bastro-\w{8}\b/)?.[0];c&&(n.class?n.class=`${n.class} ${c}`:n.class=c);const l="none"!==(p.layout??imageConfig.layout??"none");l&&(p.layout??=imageConfig.layout,p.fit??=imageConfig.objectFit??"cover",p.position??=imageConfig.objectPosition??"center");for(const t in p)t.startsWith("data-astro-cid")&&(n[t]=p[t]);const m=await resolveSrc(p.src),d=await Promise.all(s.map((async t=>await getImage({...p,src:m,format:t,widths:p.widths,densities:p.densities}))));let g=r??"png";!r&&isESMImportedImage(m)&&["gif","svg","jpg","jpeg"].includes(m.format)&&(g=m.format);const h=await getImage({...p,format:g,widths:p.widths,densities:p.densities}),f={},u={};p.sizes&&(u.sizes=p.sizes),h.srcSet.values.length>0&&(f.srcset=h.srcSet.attribute);const{class:x,...w}={...f,...h.attributes};return renderTemplate`${maybeRenderHead()}<picture${spreadAttributes(n)}> ${Object.entries(d).map((([t,e])=>{const i=p.densities||!p.densities&&!p.widths&&!l?`${e.src}${e.srcSet.values.length>0?", "+e.srcSet.attribute:""}`:e.srcSet.attribute;return renderTemplate`<source${addAttribute(i,"srcset")}${addAttribute(lookup(e.options.format??e.src)??`image/${e.options.format}`,"type")}${spreadAttributes(u)}>`}))}  <img${addAttribute(h.src,"src")}${spreadAttributes(w)}${addAttribute(x,"class")}> </picture>`}),"D:/code/image/polar-image-store/node_modules/astro/components/Picture.astro",void 0),mod=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"})),$$Astro=createAstro("https://infpik.store"),$$Font=createComponent(((t,e,i)=>{const a=t.createAstro($$Astro,e,i);a.self=$$Font;const{fontsData:o}=mod;if(!o)throw new AstroError(ExperimentalFontsNotEnabled);const{cssVariable:s,preload:n=!1}=a.props,r=o.get(s);if(!r)throw new AstroError({...FontFamilyNotFound,message:FontFamilyNotFound.message(s)});return renderTemplate`${n&&r.preloadData.map((({url:t,type:e})=>renderTemplate`<link rel="preload"${addAttribute(t,"href")} as="font"${addAttribute(`font/${e}`,"type")} crossorigin>`))}<style>${unescapeHTML(r.css)}</style>`}),"D:/code/image/polar-image-store/node_modules/astro/components/Font.astro",void 0),imageConfig={endpoint:{route:"/_image"},service:{entrypoint:"@astrojs/cloudflare/image-service",config:{}},domains:[],remotePatterns:[{protocol:"https",hostname:"**.amazonaws.com"},{protocol:"https",hostname:"polar.sh"},{protocol:"https",hostname:"**.polar.sh"},{protocol:"https",hostname:"**.amazonaws.com"},{protocol:"https",hostname:"polar.sh"},{protocol:"https",hostname:"**.polar.sh"}],responsiveStyles:!1},getImage=async t=>await getImage$1(t,imageConfig);export{$$Image as $,isRemoteAllowed as a,baseService as b,isESMImportedImage as c,getConfiguredImageService as g,imageConfig as i,lookup as l};