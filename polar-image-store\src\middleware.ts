/**
 * Middleware for handling image optimization and format negotiation
 */

import { defineMiddleware } from 'astro:middleware';
import { getOptimalFormat } from './utils/imageOptimization';

export const onRequest = defineMiddleware(async (context, next) => {
  const { request, url } = context;



  // Handle image requests with format negotiation
  if (url.pathname.startsWith('/cdn-cgi/image/')) {
    const userAgent = request.headers.get('user-agent') || '';
    const acceptHeader = request.headers.get('accept') || '';

    // Determine optimal format based on browser support
    let optimalFormat: string;
    if (acceptHeader.includes('image/avif')) {
      optimalFormat = 'avif';
    } else if (acceptHeader.includes('image/webp')) {
      optimalFormat = 'webp';
    } else {
      optimalFormat = getOptimalFormat(userAgent);
    }

    // Parse the current URL to modify format if needed
    const pathParts = url.pathname.split('/');
    if (pathParts.length >= 4) {
      const transformParams = pathParts[3];
      const imageUrl = pathParts.slice(4).join('/');

      // Check if format=auto is in the params
      if (transformParams.includes('format=auto')) {
        const newTransformParams = transformParams.replace('format=auto', `format=${optimalFormat}`);
        const newPath = `/cdn-cgi/image/${newTransformParams}/${imageUrl}`;
        
        // Create new URL with optimized format
        const newUrl = new URL(newPath, url.origin);
        return Response.redirect(newUrl.toString(), 302);
      }
    }
  }

  // Add image optimization headers
  const response = await next();

  // Add cache headers for images
  if (url.pathname.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/i)) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    response.headers.set('Vary', 'Accept');
  }

  // Add AVIF/WebP support headers
  if (url.pathname.startsWith('/cdn-cgi/image/')) {
    response.headers.set('Vary', 'Accept');
    response.headers.set('Accept-CH', 'Viewport-Width, Width, DPR');
  }

  return response;
});
