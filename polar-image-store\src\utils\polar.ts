import { Polar } from '@polar-sh/sdk';
import type { LocalProduct } from '../types/polar';

// Initialize Polar client with runtime environment variables
export function createPolarClient(env?: any) {
  // Try to get from runtime environment first (Cloudflare Pages), then fallback to build-time
  const accessToken = env?.POLAR_ACCESS_TOKEN || import.meta.env.POLAR_ACCESS_TOKEN;

  if (!accessToken) {
    throw new Error('POLAR_ACCESS_TOKEN is required');
  }

  return new Polar({
    accessToken,
    server: 'production' // Always use production
  });
}

// Convert Polar product to local product format
export function transformPolarProduct(polarProduct: any): LocalProduct | null {
  if (!polarProduct || !polarProduct.id || !polarProduct.name) {
    console.warn('Invalid polar product:', polarProduct);
    return null;
  }

  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || 'USD';

  // Extract category from metadata and trim whitespace
  const category = polarProduct.metadata?.category ?
    polarProduct.metadata.category.trim() : null;

  // Extract tags from metadata (preferred) or fallback to description
  const metadataTags = polarProduct.metadata?.tags ?
    polarProduct.metadata.tags.split(',').map((tag: string) => tag.trim()) : [];
  const descriptionTags = extractTags(polarProduct.description || '');
  const allTags = [...metadataTags, ...descriptionTags].filter(Boolean);

  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || '',
    price: price / 100, // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media: any) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: allTags,
    category,
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}

// Generate URL-friendly slug from product name
export function generateSlug(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }

  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Extract tags from description (simple implementation)
export function extractTags(description: string): string[] {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map(tag => tag.slice(1)) : [];
}

// Format price for display
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price);
}

// Create checkout URL
export async function createCheckoutUrl(productId: string): Promise<string> {
  const polar = createPolarClient();

  try {
    const checkoutLink = await polar.checkoutLinks.create({
      paymentProcessor: 'stripe',
      productId,
      allowDiscountCodes: true,
      requireBillingAddress: false,
      successUrl: `${import.meta.env.PUBLIC_SITE_URL}/success`
    });

    return checkoutLink.url;
  } catch (error) {
    console.error('Failed to create checkout URL:', error);
    throw new Error('Unable to create checkout URL');
  }
}

// Extract unique categories from products
export function extractCategories(products: LocalProduct[]): string[] {
  const categories = products
    .map(product => product.category)
    .filter((category): category is string => Boolean(category))
    .filter((category, index, array) => array.indexOf(category) === index) // Remove duplicates
    .sort();

  return categories;
}

// Get products by category
export function getProductsByCategory(products: LocalProduct[], category: string): LocalProduct[] {
  if (category === 'all') {
    return products;
  }

  return products.filter(product => product.category === category);
}

// Get category display name (convert slug to readable name)
export function getCategoryDisplayName(category: string): string {
  return category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Generate categories with counts for navigation
export function generateCategoriesWithCounts(products: LocalProduct[]): Array<{id: string, name: string, count: number}> {
  const categoryMap = new Map<string, number>();

  // Count products in each category
  products.forEach(product => {
    if (product.category) {
      const count = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, count + 1);
    }
  });

  // Convert to array and add "All" category
  const categories = Array.from(categoryMap.entries()).map(([id, count]) => ({
    id,
    name: getCategoryDisplayName(id),
    count
  }));

  // Sort by name
  categories.sort((a, b) => a.name.localeCompare(b.name));

  // Add "All" category at the beginning
  categories.unshift({
    id: 'all',
    name: 'All',
    count: products.length
  });

  return categories;
}

// Extract unique tags from products
export function extractUniqueTags(products: LocalProduct[]): string[] {
  const allTags = products
    .flatMap(product => product.tags || [])
    .filter(Boolean)
    .filter((tag, index, array) => array.indexOf(tag) === index) // Remove duplicates
    .sort();

  return allTags;
}

// Get products by tag
export function getProductsByTag(products: LocalProduct[], tag: string): LocalProduct[] {
  if (tag === 'all') {
    return products;
  }

  return products.filter(product =>
    product.tags && product.tags.includes(tag)
  );
}

// Get tag display name (convert slug to readable name)
export function getTagDisplayName(tag: string): string {
  return tag
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Generate tags with counts for navigation
export function generateTagsWithCounts(products: LocalProduct[]): Array<{id: string, name: string, count: number}> {
  const tagMap = new Map<string, number>();

  // Count products for each tag
  products.forEach(product => {
    if (product.tags) {
      product.tags.forEach(tag => {
        const count = tagMap.get(tag) || 0;
        tagMap.set(tag, count + 1);
      });
    }
  });

  // Convert to array
  const tags = Array.from(tagMap.entries()).map(([id, count]) => ({
    id,
    name: getTagDisplayName(id),
    count
  }));

  // Sort by count (descending) then by name
  tags.sort((a, b) => {
    if (b.count !== a.count) {
      return b.count - a.count; // Higher count first
    }
    return a.name.localeCompare(b.name); // Then alphabetical
  });

  // Add "All" tag at the beginning
  tags.unshift({
    id: 'all',
    name: 'All Tags',
    count: products.length
  });

  return tags;
}

export async function getTrendingProducts(days: number = 7, top: number = 10): Promise<LocalProduct[]> {
  const polar = createPolarClient();

  // Calculate time range
  const end = new Date();
  const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);

  // Fetch orders paid within the range
  let orders: any[] = [];
  try {
    const resp = await polar.orders.list({
      paid: true,
      from: start.toISOString(),
      to: end.toISOString(),
      limit: 100
    });
    orders = resp.items ?? [];
  } catch (error) {
    console.error("Failed to fetch orders for trending products", error);
    return [];
  }

  // Count products sold
  const counts = new Map<string, number>();
  orders.forEach(order => {
    (order.products || []).forEach((p: any) => {
      const id = p.id || p.product_id;
      if (!id) return;
      counts.set(id, (counts.get(id) || 0) + 1);
    });
  });

  // Sort by count and get top ids
  const topIds = [...counts.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, top)
    .map(([id]) => id);

  // Fetch product details
  const products: LocalProduct[] = [];
  await Promise.all(
    topIds.map(async id => {
      try {
        const p = await polar.products.get(id);
        const transformed = transformPolarProduct(p);
        if (transformed) products.push(transformed);
      } catch (err) {
        console.warn(`Unable to fetch product ${id}:`, err);
      }
    })
  );

  return products;
}
