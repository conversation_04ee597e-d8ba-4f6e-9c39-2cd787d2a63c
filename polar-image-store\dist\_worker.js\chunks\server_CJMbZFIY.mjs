globalThis.process??={},globalThis.process.env??={};import{f as fileExtension,j as joinPaths,s as slash,p as prependForwardSlash,r as removeTrailingForwardSlash,a as appendForwardSlash,c as collapseDuplicateTrailingSlashes,h as hasFileExtension}from"./path_h5kZAkfu.mjs";import{r as requestIs404Or500,i as isRequestServerIsland,n as notFound,a as redirectToFallback,b as redirectToDefaultLocale,c as requestHasLocale,e as normalizeTheLocale,d as defineMiddleware,S as SERVER_ISLAND_COMPONENT,f as SERVER_ISLAND_ROUTE,g as createEndpoint,R as RouteCache,s as sequence,h as findRouteToRewrite,m as matchRoute,j as RenderContext,P as PERSIST_SYMBOL,k as getSetCookiesFromResponse}from"./index_kkHkWl4A.mjs";import{l as ROUTE_TYPE_HEADER,n as REROUTE_DIRECTIVE_HEADER,D as DEFAULT_404_COMPONENT,A as AstroError,o as ActionNotFoundError,p as bold,q as red,y as yellow,t as dim,v as blue,w as clientAddressSymbol,L as LocalsNotAnObject,x as REROUTABLE_STATUS_CODES,z as responseSentSymbol}from"./astro/server_BgKLHZ62.mjs";import{D as DEFAULT_404_ROUTE,d as default404Instance,e as ensure404Route}from"./astro-designed-error-pages_BAUzKiqr.mjs";import{N as NOOP_MIDDLEWARE_FN}from"./noop-middleware_qkLZ8MMB.mjs";import"cloudflare:workers";function createI18nMiddleware(e,t,r,s){if(!e)return(e,t)=>t();const n={...e,trailingSlash:r,base:t,format:s},a=redirectToDefaultLocale(n),o=notFound(n),i=requestHasLocale(n.locales),l=redirectToFallback(n),c=(e,r)=>{const s=e.url;return s.pathname===t+"/"||s.pathname===t?a(e):i(e)?void 0:o(e,r)},d=(t,r)=>{let s=!1;const n=t.url;for(const t of n.pathname.split("/"))if(normalizeTheLocale(t)===normalizeTheLocale(e.defaultLocale)){s=!0;break}if(s){const s=n.pathname.replace(`/${e.defaultLocale}`,"");return r.headers.set("Location",s),o(t)}};return async(r,s)=>{const n=await s(),a=n.headers.get(ROUTE_TYPE_HEADER);if("no"===n.headers.get(REROUTE_DIRECTIVE_HEADER)&&void 0===e.fallback)return n;if("page"!==a&&"fallback"!==a)return n;if(requestIs404Or500(r.request,t))return n;if(isRequestServerIsland(r.request,t))return n;const{currentLocale:i}=r;switch(e.strategy){case"manual":return n;case"domains-prefix-other-locales":if(localeHasntDomain(e,i)){const e=d(r,n);if(e)return e}break;case"pathname-prefix-other-locales":{const e=d(r,n);if(e)return e;break}case"domains-prefix-always-no-redirect":if(localeHasntDomain(e,i)){const e=o(r,n);if(e)return e}break;case"pathname-prefix-always-no-redirect":{const e=o(r,n);if(e)return e;break}case"pathname-prefix-always":{const e=c(r,n);if(e)return e;break}case"domains-prefix-always":if(localeHasntDomain(e,i)){const e=c(r,n);if(e)return e}}return l(r,n)}}function localeHasntDomain(e,t){for(const r of Object.values(e.domainLookupTable))if(r===t)return!1;return!0}const NOOP_ACTIONS_MOD={server:{}},FORM_CONTENT_TYPES=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],SAFE_METHODS=["GET","HEAD","OPTIONS"];function createOriginCheckMiddleware(){return defineMiddleware(((e,t)=>{const{request:r,url:s,isPrerendered:n}=e;if(n)return t();if(SAFE_METHODS.includes(r.method))return t();const a=r.headers.get("origin")===s.origin;if(r.headers.has("content-type")){if(hasFormLikeHeader(r.headers.get("content-type"))&&!a)return new Response(`Cross-site ${r.method} form submissions are forbidden`,{status:403})}else if(!a)return new Response(`Cross-site ${r.method} form submissions are forbidden`,{status:403});return t()}))}function hasFormLikeHeader(e){if(e)for(const t of FORM_CONTENT_TYPES)if(e.toLowerCase().includes(t))return!0;return!1}function createDefaultRoutes(e){const t=new URL(e.hrefRoot);return[{instance:default404Instance,matchesComponent:e=>e.href===new URL(DEFAULT_404_COMPONENT,t).href,route:DEFAULT_404_ROUTE.route,component:DEFAULT_404_COMPONENT},{instance:createEndpoint(e),matchesComponent:e=>e.href===new URL(SERVER_ISLAND_COMPONENT,t).href,route:SERVER_ISLAND_ROUTE,component:SERVER_ISLAND_COMPONENT}]}class Pipeline{constructor(e,t,r,s,n,a,o,i=t.adapterName,l=t.clientDirectives,c=t.inlinedScripts,d=t.compressHTML,u=t.i18n,h=t.middleware,p=new RouteCache(e,r),f=(t.site?new URL(t.site):void 0),m=createDefaultRoutes(t),g=t.actions){this.logger=e,this.manifest=t,this.runtimeMode=r,this.renderers=s,this.resolve=n,this.serverLike=a,this.streaming=o,this.adapterName=i,this.clientDirectives=l,this.inlinedScripts=c,this.compressHTML=d,this.i18n=u,this.middleware=h,this.routeCache=p,this.site=f,this.defaultRoutes=m,this.actions=g,this.internalMiddleware=[],"manual"!==u?.strategy&&this.internalMiddleware.push(createI18nMiddleware(u,t.base,t.trailingSlash,t.buildFormat))}internalMiddleware;resolvedMiddleware=void 0;resolvedActions=void 0;async getMiddleware(){if(this.resolvedMiddleware)return this.resolvedMiddleware;if(this.middleware){const e=[(await this.middleware()).onRequest??NOOP_MIDDLEWARE_FN];return this.manifest.checkOrigin&&e.unshift(createOriginCheckMiddleware()),this.resolvedMiddleware=sequence(...e),this.resolvedMiddleware}return this.resolvedMiddleware=NOOP_MIDDLEWARE_FN,this.resolvedMiddleware}setActions(e){this.resolvedActions=e}async getActions(){return this.resolvedActions?this.resolvedActions:this.actions?await this.actions():NOOP_ACTIONS_MOD}async getAction(e){const t=e.split(".").map((e=>decodeURIComponent(e)));let{server:r}=await this.getActions();if(!r||"object"!=typeof r)throw new TypeError(`Expected \`server\` export in actions file to be an object. Received ${typeof r}.`);for(const e of t){if(!(e in r))throw new AstroError({...ActionNotFoundError,message:ActionNotFoundError.message(t.join("."))});r=r[e]}if("function"!=typeof r)throw new TypeError(`Expected handler for action ${t.join(".")} to be a function. Received ${typeof r}.`);return r}}const RedirectComponentInstance={default:()=>new Response(null,{status:301})},RedirectSinglePageBuiltModule={page:()=>Promise.resolve(RedirectComponentInstance),onRequest:(e,t)=>t(),renderers:[]},dateTimeFormat=new Intl.DateTimeFormat([],{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}),levels={debug:20,info:30,warn:40,error:50,silent:90};function log(e,t,r,s,n=!0){const a=e.level,o=e.dest,i={label:r,level:t,message:s,newLine:n};isLogLevelEnabled(a,t)&&o.write(i)}function isLogLevelEnabled(e,t){return levels[e]<=levels[t]}function info(e,t,r,s=!0){return log(e,"info",t,r,s)}function warn(e,t,r,s=!0){return log(e,"warn",t,r,s)}function error(e,t,r,s=!0){return log(e,"error",t,r,s)}function debug(...e){"_astroGlobalDebug"in globalThis&&globalThis._astroGlobalDebug(...e)}function getEventPrefix({level:e,label:t}){const r=`${dateTimeFormat.format(new Date)}`,s=[];return"error"===e||"warn"===e?(s.push(bold(r)),s.push(`[${e.toUpperCase()}]`)):s.push(r),t&&s.push(`[${t}]`),"error"===e?red(s.join(" ")):"warn"===e?yellow(s.join(" ")):1===s.length?dim(s[0]):dim(s[0])+" "+blue(s.splice(1).join(" "))}class Logger{options;constructor(e){this.options=e}info(e,t,r=!0){info(this.options,e,t,r)}warn(e,t,r=!0){warn(this.options,e,t,r)}error(e,t,r=!0){error(this.options,e,t,r)}debug(e,...t){debug(e,...t)}level(){return this.options.level}forkIntegrationLogger(e){return new AstroIntegrationLogger(this.options,e)}}class AstroIntegrationLogger{options;label;constructor(e,t){this.options=e,this.label=t}fork(e){return new AstroIntegrationLogger(this.options,e)}info(e){info(this.options,this.label,e)}warn(e){warn(this.options,this.label,e)}error(e){error(this.options,this.label,e)}debug(e){debug(this.label,e)}}const consoleLogDestination={write(e){let t=console.error;return levels[e.level]<levels.error&&(t=console.info),"SKIP_FORMAT"===e.label?t(e.message):t(getEventPrefix(e)+" "+e.message),!0}};function getAssetsPrefix(e,t){if(!t)return"";if("string"==typeof t)return t;const r=e.slice(1);return t[r]?t[r]:t.fallback}function createAssetLink(e,t,r){if(r){const t=getAssetsPrefix(fileExtension(e),r);return joinPaths(t,slash(e))}return t?prependForwardSlash(joinPaths(t,slash(e))):e}function createStylesheetElement(e,t,r){return"inline"===e.type?{props:{},children:e.content}:{props:{rel:"stylesheet",href:createAssetLink(e.src,t,r)},children:""}}function createStylesheetElementSet(e,t,r){return new Set(e.map((e=>createStylesheetElement(e,t,r))))}function createModuleScriptElement(e,t,r){return"external"===e.type?createModuleScriptElementWithSrc(e.value,t,r):{props:{type:"module"},children:e.value}}function createModuleScriptElementWithSrc(e,t,r){return{props:{type:"module",src:createAssetLink(e,t,r)},children:""}}function redirectTemplate({status:e,absoluteLocation:t,relativeLocation:r,from:s}){return`<!doctype html>\n<title>Redirecting to: ${r}</title>\n<meta http-equiv="refresh" content="${302===e?2:0};url=${r}">\n<meta name="robots" content="noindex">\n<link rel="canonical" href="${t}">\n<body>\n\t<a href="${r}">Redirecting ${s?`from <code>${s}</code> `:""}to <code>${r}</code></a>\n</body>`}class AppPipeline extends Pipeline{static create({logger:e,manifest:t,runtimeMode:r,renderers:s,resolve:n,serverLike:a,streaming:o,defaultRoutes:i}){return new AppPipeline(e,t,r,s,n,a,o,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,i)}headElements(e){const t=this.manifest.routes.find((t=>t.routeData===e)),r=new Set,s=new Set,n=createStylesheetElementSet(t?.styles??[]);for(const e of t?.scripts??[])"stage"in e?"head-inline"===e.stage&&s.add({props:{},children:e.children}):s.add(createModuleScriptElement(e));return{links:r,styles:n,scripts:s}}componentMetadata(){}async getComponentByRoute(e){return(await this.getModuleForRoute(e)).page()}async tryRewrite(e,t){const{newUrl:r,pathname:s,routeData:n}=findRouteToRewrite({payload:e,request:t,routes:this.manifest?.routes.map((e=>e.routeData)),trailingSlash:this.manifest.trailingSlash,buildFormat:this.manifest.buildFormat,base:this.manifest.base,outDir:this.serverLike?this.manifest.buildClientDir:this.manifest.outDir});return{newUrl:r,pathname:s,componentInstance:await this.getComponentByRoute(n),routeData:n}}async getModuleForRoute(e){for(const t of this.defaultRoutes)if(e.component===t.component)return{page:()=>Promise.resolve(t.instance),renderers:[]};if("redirect"===e.type)return RedirectSinglePageBuiltModule;if(this.manifest.pageMap){const t=this.manifest.pageMap.get(e.component);if(!t)throw new Error(`Unexpectedly unable to find a component instance for route ${e.route}`);return await t()}if(this.manifest.pageModule)return this.manifest.pageModule;throw new Error("Astro couldn't find the correct page to render, probably because it wasn't correctly mapped for SSR usage. This is an internal error, please file an issue.")}}class App{#e;#t;#r=new Logger({dest:consoleLogDestination,level:"info"});#s;#n;#a;constructor(e,t=!0){this.#e=e,this.#t={routes:e.routes.map((e=>e.routeData))},ensure404Route(this.#t),this.#s=removeTrailingForwardSlash(this.#e.base),this.#n=this.#o(t),this.#a=new AstroIntegrationLogger(this.#r.options,this.#e.adapterName)}getAdapterLogger(){return this.#a}#o(e=!1){return AppPipeline.create({logger:this.#r,manifest:this.#e,runtimeMode:"production",renderers:this.#e.renderers,defaultRoutes:createDefaultRoutes(this.#e),resolve:async e=>{if(!(e in this.#e.entryModules))throw new Error(`Unable to resolve [${e}]`);const t=this.#e.entryModules[e];return t.startsWith("data:")||0===t.length?t:createAssetLink(t,this.#e.base,this.#e.assetsPrefix)},serverLike:!0,streaming:e})}set setManifestData(e){this.#t=e}removeBase(e){return e.startsWith(this.#e.base)?e.slice(this.#s.length+1):e}#i(e){const t=new URL(e.url),r=prependForwardSlash(this.removeBase(t.pathname));try{return decodeURI(r)}catch(e){return this.getAdapterLogger().error(e.toString()),r}}match(e,t=!1){const r=new URL(e.url);if(this.#e.assets.has(r.pathname))return;let s=this.#l(e);s||(s=prependForwardSlash(this.removeBase(r.pathname)));let n=matchRoute(decodeURI(s),this.#t);if(n){if(t)return n;if(!n.prerender)return n}}#l(e){let t;const r=new URL(e.url);if(this.#e.i18n&&("domains-prefix-always"===this.#e.i18n.strategy||"domains-prefix-other-locales"===this.#e.i18n.strategy||"domains-prefix-always-no-redirect"===this.#e.i18n.strategy)){let s=e.headers.get("X-Forwarded-Host"),n=e.headers.get("X-Forwarded-Proto");if(n?n+=":":n=r.protocol,s||(s=e.headers.get("Host")),s&&n){s=s.split(":")[0];try{let e;const a=new URL(`${n}//${s}`);for(const[t,r]of Object.entries(this.#e.i18n.domainLookupTable)){const s=new URL(t);if(a.host===s.host&&a.protocol===s.protocol){e=r;break}}e&&(t=prependForwardSlash(joinPaths(normalizeTheLocale(e),this.removeBase(r.pathname))),r.pathname.endsWith("/")&&(t=appendForwardSlash(t)))}catch(e){this.#r.error("router",`Astro tried to parse ${n}//${s} as an URL, but it threw a parsing error. Check the X-Forwarded-Host and X-Forwarded-Proto headers.`),this.#r.error("router",`Error: ${e}`)}}}return t}#c(e){const{trailingSlash:t}=this.#e;if("/"===e||e.startsWith("/_"))return e;const r=collapseDuplicateTrailingSlashes(e,"never"!==t);return r!==e?r:"ignore"===t?e:"always"!==t||hasFileExtension(e)?"never"===t?removeTrailingForwardSlash(e):e:appendForwardSlash(e)}async render(e,t){let r,s,n,a;const o=new URL(e.url),i=this.#c(o.pathname),l=t?.prerenderedErrorPageFetch??fetch;if(i!==o.pathname){const t="GET"===e.method?301:308;return new Response(redirectTemplate({status:t,relativeLocation:o.pathname,absoluteLocation:i,from:e.url}),{status:t,headers:{location:i+o.search}})}if(a=t?.addCookieHeader,n=t?.clientAddress??Reflect.get(e,clientAddressSymbol),r=t?.routeData,s=t?.locals,r&&(this.#r.debug("router","The adapter "+this.#e.adapterName+" provided a custom RouteData for ",e.url),this.#r.debug("router","RouteData:\n"+r)),s&&"object"!=typeof s){const t=new AstroError(LocalsNotAnObject);return this.#r.error(null,t.stack),this.#d(e,{status:500,error:t,clientAddress:n,prerenderedErrorPageFetch:l})}if(r||(r=this.match(e),this.#r.debug("router","Astro matched the following route for "+e.url),this.#r.debug("router","RouteData:\n"+r)),r||(r=this.#t.routes.find((e=>"404.astro"===e.component||e.component===DEFAULT_404_COMPONENT))),!r)return this.#r.debug("router","Astro hasn't found routes that match "+e.url),this.#r.debug("router","Here's the available routes:\n",this.#t),this.#d(e,{locals:s,status:404,clientAddress:n,prerenderedErrorPageFetch:l});const c=this.#i(e),d=this.#u(r,c);let u,h;try{const t=await this.#n.getModuleForRoute(r),a=await RenderContext.create({pipeline:this.#n,locals:s,pathname:c,request:e,routeData:r,status:d,clientAddress:n});h=a.session,u=await a.render(await t.page())}catch(t){return this.#r.error(null,t.stack||t.message||String(t)),this.#d(e,{locals:s,status:500,error:t,clientAddress:n,prerenderedErrorPageFetch:l})}finally{await(h?.[PERSIST_SYMBOL]())}if(REROUTABLE_STATUS_CODES.includes(u.status)&&"no"!==u.headers.get(REROUTE_DIRECTIVE_HEADER))return this.#d(e,{locals:s,response:u,status:u.status,error:500===u.status?null:void 0,clientAddress:n,prerenderedErrorPageFetch:l});if(u.headers.has(REROUTE_DIRECTIVE_HEADER)&&u.headers.delete(REROUTE_DIRECTIVE_HEADER),a)for(const e of App.getSetCookieFromResponse(u))u.headers.append("set-cookie",e);return Reflect.set(u,responseSentSymbol,!0),u}setCookieHeaders(e){return getSetCookiesFromResponse(e)}static getSetCookieFromResponse=getSetCookiesFromResponse;async#d(e,{locals:t,status:r,response:s,skipMiddleware:n=!1,error:a,clientAddress:o,prerenderedErrorPageFetch:i}){const l=`/${r}${"always"===this.#e.trailingSlash?"/":""}`,c=matchRoute(l,this.#t),d=new URL(e.url);if(c){if(c.prerender){const t=c.route.endsWith(`/${r}`)?".html":"",n=new URL(`${this.#s}/${r}${t}`,d);if(n.toString()!==e.url){const e=await i(n.toString()),t={status:r,removeContentEncodingHeaders:!0};return this.#h(e,s,t)}}const l=await this.#n.getModuleForRoute(c);let u;try{const i=await RenderContext.create({locals:t,pipeline:this.#n,middleware:n?NOOP_MIDDLEWARE_FN:void 0,pathname:this.#i(e),request:e,routeData:c,status:r,props:{error:a},clientAddress:o});u=i.session;const d=await i.render(await l.page());return this.#h(d,s)}catch{if(!1===n)return this.#d(e,{locals:t,status:r,response:s,skipMiddleware:!0,clientAddress:o,prerenderedErrorPageFetch:i})}finally{await(u?.[PERSIST_SYMBOL]())}}const u=this.#h(new Response(null,{status:r}),s);return Reflect.set(u,responseSentSymbol,!0),u}#h(e,t,r){let s=e.headers;if(r?.removeContentEncodingHeaders&&(s=new Headers(s),s.delete("Content-Encoding"),s.delete("Content-Length")),!t)return void 0!==r?new Response(e.body,{status:r.status,statusText:e.statusText,headers:s}):e;const n=r?.status?r.status:200===t.status?e.status:t.status;try{t.headers.delete("Content-type")}catch{}const a=new Map([...Array.from(s),...Array.from(t.headers)]),o=new Headers;for(const[e,t]of a)o.set(e,t);return new Response(e.body,{status:n,statusText:200===n?e.statusText:t.statusText,headers:o})}#u(e,t){if(!e.pattern.test(t))for(const r of e.fallbackRoutes)if(r.pattern.test(t))return 302;const r=removeTrailingForwardSlash(e.route);return r.endsWith("/404")?404:r.endsWith("/500")?500:200}}async function handle(e,t,r,s,n){const{pathname:a}=new URL(r.url),o="SESSION";if(globalThis.__env__??={},globalThis.__env__[o]=s[o],e.assets.has(a))return s.ASSETS.fetch(r.url.replace(/\.html$/,""));const i=t.match(r);if(!i){const e=await s.ASSETS.fetch(r.url.replace(/index.html$/,"").replace(/\.html$/,""));if(404!==e.status)return e}Reflect.set(r,Symbol.for("astro.clientAddress"),r.headers.get("cf-connecting-ip")),process.env.ASTRO_STUDIO_APP_TOKEN??=(()=>{if("string"==typeof s.ASTRO_STUDIO_APP_TOKEN)return s.ASTRO_STUDIO_APP_TOKEN})();const l={runtime:{env:s,cf:r.cf,caches:caches,ctx:{waitUntil:e=>n.waitUntil(e),passThroughOnException:()=>{throw new Error("`passThroughOnException` is currently not available in Cloudflare Pages. See https://developers.cloudflare.com/pages/platform/known-issues/#pages-functions.")},props:{}}}},c=await t.render(r,{routeData:i,locals:l,prerenderedErrorPageFetch:async e=>s.ASSETS.fetch(e.replace(/\.html$/,""))});if(t.setCookieHeaders)for(const e of t.setCookieHeaders(c))c.headers.append("Set-Cookie",e);return c}function createExports(e){const t=new App(e);return{default:{fetch:async(r,s,n)=>await handle(e,t,r,s,n)}}}export{createExports as c};