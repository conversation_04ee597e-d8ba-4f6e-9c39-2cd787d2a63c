import type { MiddlewareHandler } from 'astro';

export const performanceMiddleware: MiddlewareHandler = async (context, next) => {
  const response = await next();

  // Add performance and security headers
  const headers = new Headers(response.headers);

  // Cache control for static assets
  const url = new URL(context.request.url);
  const pathname = url.pathname;

  if (pathname.match(/\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|webp|avif|ico)$/)) {
    // Static assets - cache for 1 year
    headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  } else if (pathname.startsWith('/api/')) {
    // API routes - cache for 5 minutes
    headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
  } else if (pathname.startsWith('/products/')) {
    // Product pages - cache for 1 hour
    headers.set('Cache-Control', 'public, max-age=3600, s-maxage=3600');
  } else {
    // Other pages - cache for 10 minutes
    headers.set('Cache-Control', 'public, max-age=600, s-maxage=600');
  }

  // Performance headers
  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'DENY');
  headers.set('X-XSS-Protection', '1; mode=block');
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Preload critical resources
  if (pathname === '/') {
    headers.set('Link', '</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin');
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
};
