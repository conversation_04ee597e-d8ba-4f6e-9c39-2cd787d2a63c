globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:r,locals:e})=>{try{const t=r.searchParams.get("email"),o=e?.runtime?.env,s=createPolarClient(o),a=o?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(t&&t.trim())try{const r=await s.customers.list({email:t.trim(),organizationId:a}),e=r.result?.items?.[0];if(e?.id){const r=await s.customerSessions.create({customerId:e.id});if(r.customerPortalUrl)return Response.redirect(r.customerPortalUrl,302)}}catch(r){console.error("Error creating customer session:",r)}try{const r=(await s.organizations.get({id:a})).slug;if(r){const e=`https://polar.sh/${r}/portal`;return Response.redirect(e,302)}}catch(r){console.error("Error fetching organization info:",r)}const n="https://polar.sh/portal";return Response.redirect(n,302)}catch(r){return console.error("Portal redirect error:",r),new Response(JSON.stringify({error:"Failed to redirect to customer portal"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};