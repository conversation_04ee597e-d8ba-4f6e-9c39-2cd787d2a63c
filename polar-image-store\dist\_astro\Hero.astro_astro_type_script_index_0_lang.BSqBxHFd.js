document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("categoryScroll");if(o){let e=!1,t,c;o.addEventListener("touchstart",s=>{e=!0,t=s.touches[0].pageX-o.offsetLeft,c=o.scrollLeft}),o.addEventListener("touchend",()=>{e=!1}),o.addEventListener("touchmove",s=>{if(!e)return;const l=(s.touches[0].pageX-o.offsetLeft-t)*2;o.scrollLeft=c-l}),o.addEventListener("mousedown",s=>{e=!0,t=s.pageX-o.offsetLeft,c=o.scrollLeft,o.style.cursor="grabbing"}),o.addEventListener("mouseleave",()=>{e=!1,o.style.cursor="grab"}),o.addEventListener("mouseup",()=>{e=!1,o.style.cursor="grab"}),o.addEventListener("mousemove",s=>{if(!e)return;s.preventDefault();const l=(s.pageX-o.offsetLeft-t)*2;o.scrollLeft=c-l}),o.style.cursor="grab";const n=document.querySelectorAll(".category-tab");n.forEach(s=>{s.addEventListener("click",d=>{const l=d.currentTarget.dataset.category;n.forEach(y=>{y.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),y.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),s.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),s.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),window.location.pathname==="/"?(console.log("📡 Dispatching categoryChange event for homepage:",l),window.dispatchEvent(new CustomEvent("categoryChange",{detail:{categoryId:l}}))):l==="all"?window.location.href="/products":window.location.href=`/products/category/${l}`})});const a=document.querySelectorAll(".tag-tab");a.forEach(s=>{s.addEventListener("click",d=>{const l=d.currentTarget.dataset.tag;a.forEach(m=>{m.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),m.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),d.currentTarget.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),d.currentTarget.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),setTimeout(()=>{l==="all"?window.location.href="/products":window.location.href=`/products/tag/${l}`},150)})})}const i=document.getElementById("heroSearchInput"),r=document.getElementById("heroSearchResults");let f;function h(){return window.innerWidth<768}function v(e){if(h()){e.blur();const t=e.value.trim();window.openSearchModal?.(t)}}function g(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(a=>a!==e);c.unshift(e);const n=c.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(n))}catch(t){console.error("Failed to save recent search:",t)}}function b(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(n=>n!==e);localStorage.setItem("recentSearches",JSON.stringify(c))}catch(t){console.error("Failed to remove recent search:",t)}}function u(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&r){const t=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="heroSearchClearRecent" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((n,a)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${a<e.length-1?"border-b border-primary-100":""}">
                  <button class="hero-recent-search-item flex-1 text-left" data-query="${n}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${n}</span>
                    </div>
                  </button>
                  <button class="hero-delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${n}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>
          `;r.innerHTML=t,r.classList.remove("hidden"),r.querySelectorAll(".hero-recent-search-item").forEach(n=>{n.addEventListener("click",()=>{const a=n.getAttribute("data-query");a&&i&&(i.value=a,p(a))})}),r.querySelectorAll(".hero-delete-recent-search").forEach(n=>{n.addEventListener("click",a=>{a.stopPropagation();const s=n.getAttribute("data-query");s&&(b(s),u())})}),r.querySelector("#heroSearchClearRecent")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),r?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}async function p(e){if(!(!e.trim()||!r))try{r.innerHTML='<div class="p-4 text-center text-primary-500">Searching...</div>',r.classList.remove("hidden");const c=await(await fetch(`/api/search?q=${encodeURIComponent(e)}`)).json();c.results&&c.results.length>0?(g(e),w(c.results,e)):x(e)}catch(t){console.error("Search error:",t),r.innerHTML='<div class="p-4 text-center text-red-500">Search failed. Please try again.</div>'}}function w(e,t){if(!r)return;const c=e.map(a=>`
        <button class="hero-search-result-item w-full p-3 hover:bg-primary-50 transition-colors text-left border-b border-primary-100 last:border-b-0" data-url="/products/tag/${a.slug}">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div>
                <div class="font-medium text-primary-900">${a.displayName}</div>
                <div class="text-sm text-primary-500">${a.count} images</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join(""),n=`
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <span class="text-xs text-primary-500 font-medium">Search All images for "${t}"</span>
        </div>
        ${c}
        ${e.length>5?`<div class="p-3 text-center text-sm text-primary-500">Showing ${Math.min(5,e.length)} of ${e.length} tags</div>`:""}
      `;r.innerHTML=n,r.classList.remove("hidden"),r.querySelectorAll(".hero-search-result-item").forEach(a=>{a.addEventListener("click",()=>{const s=a.getAttribute("data-url");s&&(window.location.href=s)})})}function x(e){r&&(r.innerHTML=`
        <div class="p-4 text-center">
          <div class="text-primary-500 mb-2">No images found for "${e}"</div>
          <a href="/products" class="text-accent-600 hover:text-accent-700 text-sm font-medium">Browse all images →</a>
        </div>
      `,r.classList.remove("hidden"))}function L(){const e=i?.value.trim();e?(clearTimeout(f),f=setTimeout(()=>{p(e)},300)):u()}i&&(i.addEventListener("focus",e=>{h()?v(e.target):i.value.trim()||u()}),i.addEventListener("click",e=>{h()&&v(e.target)}),i.addEventListener("input",L),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=i.value.trim();t&&(h()?window.openSearchModal?.(t):(g(t),window.location.href=`/products?search=${encodeURIComponent(t)}`))}})),document.addEventListener("click",e=>{r&&i&&!r.contains(e.target)&&!i.contains(e.target)&&r.classList.add("hidden")})});
