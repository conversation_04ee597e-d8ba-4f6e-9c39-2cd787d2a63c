---
import type { LocalProduct } from '../types/polar';
import { createPolarClient, transformPolarProduct, formatPrice } from '../utils/polar';
import OptimizedImage from './OptimizedImage.astro';

export interface Props {
  currentProduct: LocalProduct;
}

const { currentProduct } = Astro.props;

// Function to calculate similarity score between two products based on shared tags
function calculateSimilarity(product1: LocalProduct, product2: LocalProduct): number {
  if (!product1.tags || !product2.tags) return 0;
  
  const tags1 = new Set(product1.tags);
  const tags2 = new Set(product2.tags);
  
  // Count shared tags
  let sharedTags = 0;
  for (const tag of tags1) {
    if (tags2.has(tag)) {
      sharedTags++;
    }
  }
  
  return sharedTags;
}

// Fetch all products and find related ones
let relatedProducts: LocalProduct[] = [];

try {
  const polar = createPolarClient();
  const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

  if (organizationId && currentProduct.tags && currentProduct.tags.length > 0) {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Find products with shared tags, exclude current product
    const candidateProducts = allProducts.filter(product => 
      product.id !== currentProduct.id && 
      product.tags && 
      product.tags.length > 0
    );

    // Calculate similarity scores and filter products with at least 1 shared tag
    const productsWithScores = candidateProducts
      .map(product => ({
        product,
        similarity: calculateSimilarity(currentProduct, product)
      }))
      .filter(item => item.similarity > 0) // Only products with at least 1 shared tag
      .sort((a, b) => b.similarity - a.similarity) // Sort by similarity descending
      .slice(0, 6) // Take max 6 products
      .map(item => item.product);

    relatedProducts = productsWithScores;
  }
} catch (error) {
  console.error('Error fetching related products:', error);
}
---

{relatedProducts.length > 0 && (
  <section class="mt-16">
    <div class="mb-8">
      <h2 class="text-3xl font-bold text-primary-900 mb-4">Related Images</h2>
      <p class="text-primary-600">Discover similar images you might like</p>
    </div>

    <div class="relative">
      <!-- Slider container -->
      <div class="overflow-x-auto scrollbar-hide">
        <div class="flex gap-6 pb-4" style="width: max-content;">
          {relatedProducts.map((product) => (
            <div class="flex-none w-80">
              <div class="group bg-white rounded-2xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl hover:shadow-primary-500/10">
                {product.images.length > 0 && (
                  <div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50">
                    <OptimizedImage
                      src={product.images[0]}
                      alt={product.name}
                      preset="related"
                      loading="lazy"
                      class="w-full h-full object-cover transition-all duration-300 group-hover:scale-105"
                    />

                    <!-- Gradient overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <!-- Hover actions -->
                    <div class="absolute inset-0 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
                      <a
                        href={`/products/${product.slug}`}
                        class="flex items-center gap-2 px-4 py-2 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-medium text-sm transition-all duration-200 hover:bg-white hover:scale-105"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </a>
                    </div>

                    <!-- Price badge -->
                    <div class="absolute top-3 right-3 px-2 py-1 bg-white/95 backdrop-blur-sm rounded-full">
                      <span class="text-sm font-bold text-primary-900">{formatPrice(product.price, product.currency)}</span>
                    </div>
                  </div>
                )}

                <div class="p-4">
                  <!-- Tags -->
                  {product.tags && product.tags.length > 0 && (
                    <div class="mb-2">
                      <div class="flex flex-wrap gap-1">
                        {product.tags.slice(0, 2).map(tag => (
                          <span class="inline-flex items-center px-2 py-0.5 bg-accent-100 text-accent-700 text-xs font-medium rounded-full">
                            #{tag}
                          </span>
                        ))}
                        {product.tags.length > 2 && (
                          <span class="inline-flex items-center px-2 py-0.5 bg-primary-100 text-primary-600 text-xs font-medium rounded-full">
                            +{product.tags.length - 2}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <!-- Title -->
                  <h3 class="text-lg font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors">
                    {product.name}
                  </h3>

                  <!-- Description -->
                  <p class="text-primary-600 text-sm line-clamp-2 leading-relaxed">
                    {product.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <!-- Scroll indicators (optional) -->
      <div class="flex justify-center mt-4 gap-2">
        {relatedProducts.map((_, index) => (
          <div class="w-2 h-2 bg-primary-200 rounded-full"></div>
        ))}
      </div>
    </div>
  </section>
)}

<style>
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
