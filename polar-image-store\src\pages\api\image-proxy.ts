/**
 * Image proxy API endpoint for Cloudflare Image Transform
 * Handles format negotiation and optimization
 */

import type { APIRoute } from 'astro';
import { getOptimalFormat, getOptimizedImageUrl, type ImageTransformOptions } from '../../utils/imageOptimization';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    // Get query parameters
    const searchParams = url.searchParams;
    const imageUrl = searchParams.get('url');
    const width = searchParams.get('width');
    const height = searchParams.get('height');
    const quality = searchParams.get('quality');
    const format = searchParams.get('format');
    const fit = searchParams.get('fit');

    if (!imageUrl) {
      return new Response('Missing image URL', { status: 400 });
    }

    // Get user agent for format negotiation
    const userAgent = request.headers.get('user-agent') || '';
    const acceptHeader = request.headers.get('accept') || '';

    // Determine optimal format
    let optimalFormat: string;
    if (format && format !== 'auto') {
      optimalFormat = format;
    } else {
      // Check Accept header first
      if (acceptHeader.includes('image/avif')) {
        optimalFormat = 'avif';
      } else if (acceptHeader.includes('image/webp')) {
        optimalFormat = 'webp';
      } else {
        optimalFormat = getOptimalFormat(userAgent);
      }
    }

    // Build transform options
    const transformOptions: ImageTransformOptions = {
      format: optimalFormat as any,
      fit: (fit as any) || 'scale-down'
    };

    if (width) transformOptions.width = parseInt(width);
    if (height) transformOptions.height = parseInt(height);
    if (quality) transformOptions.quality = parseInt(quality);

    // Generate optimized URL
    const optimizedUrl = getOptimizedImageUrl(imageUrl, transformOptions);

    // If it's already optimized or local, redirect directly
    if (optimizedUrl === imageUrl || imageUrl.startsWith('/')) {
      return Response.redirect(imageUrl, 302);
    }

    // For external images, use Cloudflare Image Transform
    const baseUrl = new URL(request.url).origin;
    const fullOptimizedUrl = `${baseUrl}${optimizedUrl}`;

    return Response.redirect(fullOptimizedUrl, 302);

  } catch (error) {
    console.error('Image proxy error:', error);
    return new Response('Internal server error', { status: 500 });
  }
};

export const POST: APIRoute = async () => {
  return new Response('Method not allowed', { status: 405 });
};
