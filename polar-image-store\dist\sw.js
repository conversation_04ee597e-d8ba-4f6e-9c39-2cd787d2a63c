const CACHE_NAME="infpik-v1",STATIC_CACHE_NAME="infpik-static-v1",STATIC_RESOURCES=["/","/fonts/inter.woff2","/logo.svg","/favicon.svg","/manifest.json"];self.addEventListener("install",(e=>{e.waitUntil(caches.open(STATIC_CACHE_NAME).then((e=>e.addAll(STATIC_RESOURCES))).then((()=>self.skipWaiting())))})),self.addEventListener("activate",(e=>{e.waitUntil(caches.keys().then((e=>Promise.all(e.map((e=>{if(e!==CACHE_NAME&&e!==STATIC_CACHE_NAME)return caches.delete(e)}))))).then((()=>self.clients.claim())))})),self.addEventListener("fetch",(e=>{const{request:t}=e,n=new URL(t.url);"GET"===t.method&&n.origin===self.location.origin&&e.respondWith(caches.match(t).then((e=>e||fetch(t).then((e=>{if(!e||200!==e.status||"basic"!==e.type)return e;const s=e.clone();let i=CACHE_NAME;return n.pathname.match(/\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|webp|avif|ico)$/)&&(i=STATIC_CACHE_NAME),caches.open(i).then((e=>{e.put(t,s)})),e})))))}));