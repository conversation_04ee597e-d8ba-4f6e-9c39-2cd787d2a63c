function appendForwardSlash(n){return n.endsWith("/")?n:n+"/"}function prependForwardSlash(n){return"/"===n[0]?n:"/"+n}globalThis.process??={},globalThis.process.env??={};const MANY_TRAILING_SLASHES=/\/{2,}$/g;function collapseDuplicateTrailingSlashes(n,s){return n?n.replace(MANY_TRAILING_SLASHES,s?"/":"")||"/":n}function removeTrailingForwardSlash(n){return n.endsWith("/")?n.slice(0,n.length-1):n}function removeLeadingForwardSlash(n){return n.startsWith("/")?n.substring(1):n}function trimSlashes(n){return n.replace(/^\/|\/$/g,"")}function isString(n){return"string"==typeof n||n instanceof String}function joinPaths(...n){return n.filter(isString).map(((s,t)=>0===t?removeTrailingForwardSlash(s):t===n.length-1?removeLeadingForwardSlash(s):trimSlashes(s))).join("/")}function isRemotePath(n){return/^(?:http|ftp|https|ws):?\/\//.test(n)||n.startsWith("data:")}function slash(n){return n.replace(/\\/g,"/")}function fileExtension(n){const s=n.split(".").pop();return s!==n?`.${s}`:""}const WITH_FILE_EXT=/\/[^/]+\.\w+$/;function hasFileExtension(n){return WITH_FILE_EXT.test(n)}export{appendForwardSlash as a,collapseDuplicateTrailingSlashes as c,fileExtension as f,hasFileExtension as h,isRemotePath as i,joinPaths as j,prependForwardSlash as p,removeTrailingForwardSlash as r,slash as s,trimSlashes as t};