import { Checkout } from "@polar-sh/astro";

export const prerender = false;

// Note: Checkout API needs to be handled differently for runtime env vars
// For now, keeping build-time env vars as Polar Checkout might not support runtime injection
export const GET = Checkout({
  accessToken: import.meta.env.POLAR_ACCESS_TOKEN,
  successUrl: `${import.meta.env.PUBLIC_SITE_URL}/success`,
  server: 'production' // Always use production
});
