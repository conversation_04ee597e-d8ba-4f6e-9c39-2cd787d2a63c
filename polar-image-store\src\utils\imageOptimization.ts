/**
 * Image optimization utilities for Cloudflare Image Transform
 * Provides functions to generate optimized image URLs with AVIF/WebP fallback
 */

export interface ImageTransformOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'auto' | 'avif' | 'webp' | 'jpeg' | 'png';
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  sharpen?: number;
  blur?: number;
  saturation?: number;
  brightness?: number;
  contrast?: number;
  gamma?: number;
}

export interface ResponsiveImageOptions extends ImageTransformOptions {
  sizes?: number[];
  densities?: number[];
}

/**
 * Generate Cloudflare Image Transform URL
 * @param imageUrl - Original image URL
 * @param options - Transform options
 * @returns Optimized image URL
 */
export function getOptimizedImageUrl(imageUrl: string, options: ImageTransformOptions = {}): string {
  // If it's already a local image or placeholder, return as-is
  if (imageUrl.startsWith('/') || imageUrl.includes('placeholder')) {
    return imageUrl;
  }

  const {
    width,
    height,
    quality = 85,
    format = 'auto',
    fit = 'scale-down',
    sharpen,
    blur,
    saturation,
    brightness,
    contrast,
    gamma
  } = options;

  // Build transformation parameters
  const params: string[] = [];
  
  if (width) params.push(`width=${width}`);
  if (height) params.push(`height=${height}`);
  if (quality) params.push(`quality=${quality}`);
  if (format) params.push(`format=${format}`);
  if (fit) params.push(`fit=${fit}`);
  if (sharpen) params.push(`sharpen=${sharpen}`);
  if (blur) params.push(`blur=${blur}`);
  if (saturation) params.push(`saturation=${saturation}`);
  if (brightness) params.push(`brightness=${brightness}`);
  if (contrast) params.push(`contrast=${contrast}`);
  if (gamma) params.push(`gamma=${gamma}`);

  const transformParams = params.join(',');
  
  // Return Cloudflare Image Transform URL
  return `/cdn-cgi/image/${transformParams}/${imageUrl}`;
}

/**
 * Generate responsive image srcset for different screen sizes
 * @param imageUrl - Original image URL
 * @param options - Responsive image options
 * @returns Object with src and srcset
 */
export function getResponsiveImageUrls(imageUrl: string, options: ResponsiveImageOptions = {}) {
  const {
    sizes = [320, 640, 960, 1280, 1920],
    densities = [1, 2],
    ...transformOptions
  } = options;

  // Generate main src (largest size)
  const mainSrc = getOptimizedImageUrl(imageUrl, {
    ...transformOptions,
    width: Math.max(...sizes)
  });

  // Generate srcset for different sizes
  const srcsetEntries: string[] = [];
  
  for (const size of sizes) {
    for (const density of densities) {
      const width = size * density;
      const url = getOptimizedImageUrl(imageUrl, {
        ...transformOptions,
        width
      });
      srcsetEntries.push(`${url} ${width}w`);
    }
  }

  return {
    src: mainSrc,
    srcset: srcsetEntries.join(', ')
  };
}

/**
 * Get optimized image for specific use cases
 */
export const ImagePresets = {
  // Product card images
  productCard: (imageUrl: string) => getOptimizedImageUrl(imageUrl, {
    width: 800,
    height: 600,
    quality: 85,
    format: 'auto',
    fit: 'cover'
  }),

  // Product detail main image
  productDetail: (imageUrl: string) => getOptimizedImageUrl(imageUrl, {
    width: 1200,
    height: 900,
    quality: 90,
    format: 'auto',
    fit: 'contain'
  }),

  // Thumbnail images
  thumbnail: (imageUrl: string) => getOptimizedImageUrl(imageUrl, {
    width: 150,
    height: 150,
    quality: 80,
    format: 'auto',
    fit: 'cover'
  }),

  // Hero/banner images
  hero: (imageUrl: string) => getOptimizedImageUrl(imageUrl, {
    width: 1920,
    height: 1080,
    quality: 90,
    format: 'auto',
    fit: 'cover'
  }),

  // Related images
  related: (imageUrl: string) => getOptimizedImageUrl(imageUrl, {
    width: 600,
    height: 450,
    quality: 85,
    format: 'auto',
    fit: 'cover'
  })
};

/**
 * Generate sizes attribute for responsive images
 * @param breakpoints - Object with breakpoint sizes
 * @returns sizes attribute string
 */
export function generateSizesAttribute(breakpoints: Record<string, string> = {}) {
  const defaultBreakpoints = {
    '(max-width: 640px)': '100vw',
    '(max-width: 1024px)': '50vw',
    '(max-width: 1280px)': '33vw',
    ...breakpoints
  };

  const sizeEntries = Object.entries(defaultBreakpoints);
  const sizesArray = sizeEntries.slice(0, -1).map(([query, size]) => `${query} ${size}`);
  
  // Add default size (last entry value)
  const defaultSize = sizeEntries[sizeEntries.length - 1][1];
  sizesArray.push(defaultSize);

  return sizesArray.join(', ');
}

/**
 * Check if browser supports AVIF format
 * @param userAgent - User agent string
 * @returns boolean
 */
export function supportsAVIF(userAgent?: string): boolean {
  if (!userAgent) return false;
  
  // Chrome 85+, Firefox 93+, Safari 16.1+
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
  const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
  const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);

  if (chromeMatch && parseInt(chromeMatch[1]) >= 85) return true;
  if (firefoxMatch && parseInt(firefoxMatch[1]) >= 93) return true;
  if (safariMatch && parseInt(safariMatch[1]) >= 16) return true;

  return false;
}

/**
 * Check if browser supports WebP format
 * @param userAgent - User agent string
 * @returns boolean
 */
export function supportsWebP(userAgent?: string): boolean {
  if (!userAgent) return false;
  
  // Chrome 23+, Firefox 65+, Safari 14+, Edge 18+
  const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
  const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
  const safariMatch = userAgent.match(/Version\/(\d+).*Safari/);
  const edgeMatch = userAgent.match(/Edge\/(\d+)/);

  if (chromeMatch && parseInt(chromeMatch[1]) >= 23) return true;
  if (firefoxMatch && parseInt(firefoxMatch[1]) >= 65) return true;
  if (safariMatch && parseInt(safariMatch[1]) >= 14) return true;
  if (edgeMatch && parseInt(edgeMatch[1]) >= 18) return true;

  return false;
}

/**
 * Get optimal image format based on browser support
 * @param userAgent - User agent string
 * @returns Optimal format
 */
export function getOptimalFormat(userAgent?: string): 'avif' | 'webp' | 'jpeg' {
  if (supportsAVIF(userAgent)) return 'avif';
  if (supportsWebP(userAgent)) return 'webp';
  return 'jpeg';
}
