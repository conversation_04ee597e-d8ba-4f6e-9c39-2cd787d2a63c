globalThis.process??={},globalThis.process.env??={};import{W as Webhooks}from"../../chunks/index_C4LTQoxk.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,POST=Webhooks({webhookSecret:"polar_whs_f1Gwbp88YdQuQK5pnVyFPd8YcawooY9oOMbB81N1jLU",onPayload:async e=>{console.log("Received webhook:",e.type);try{switch(e.type){case"product.created":await handleProductCreated(e.data);break;case"product.updated":await handleProductUpdated(e.data);break;case"product.deleted":await handleProductDeleted(e.data);break;case"order.paid":await handleOrderPaid(e.data);break;default:console.log("Unhandled webhook type:",e.type)}}catch(e){throw console.error("Error processing webhook:",e),e}}});async function handleProductCreated(e){console.log("Product created:",e.id,e.name)}async function handleProductUpdated(e){console.log("Product updated:",e.id,e.name)}async function handleProductDeleted(e){console.log("Product deleted:",e.id)}async function handleOrderPaid(e){console.log("Order paid:",e.id)}const _page=Object.freeze(Object.defineProperty({__proto__:null,POST:POST,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};