globalThis.process??={},globalThis.process.env??={};import{o as objectType,B as Benefit$inboundSchema,l as literalType,a as Benefit$outboundSchema,n as nullableType,u as unionType,C as Customer$inboundSchema,s as stringType,b as booleanType,c as BenefitGrantDiscordProperties$inboundSchema,d as BenefitGrantGitHubRepositoryProperties$inboundSchema,e as BenefitGrantDownloadablesProperties$inboundSchema,f as BenefitGrantLicenseKeysProperties$inboundSchema,g as BenefitGrantCustomProperties$inboundSchema,h as BenefitGrantError$inboundSchema,r as remap,i as BenefitGrantDiscordProperties$outboundSchema,j as BenefitGrantGitHubRepositoryProperties$outboundSchema,k as BenefitGrantDownloadablesProperties$outboundSchema,m as BenefitGrantLicenseKeysProperties$outboundSchema,p as BenefitGrantCustomProperties$outboundSchema,q as Customer$outboundSchema,t as dateType,v as BenefitGrantError$outboundSchema,w as Checkout$inboundSchema,x as Checkout$outboundSchema,O as Order$inboundSchema,y as Order$outboundSchema,z as Organization$inboundSchema,A as Organization$outboundSchema,D as Product$inboundSchema,E as Product$outboundSchema,R as Refund$inboundSchema,F as Refund$outboundSchema,S as Subscription$inboundSchema,G as Subscription$outboundSchema,H as CustomerState$inboundSchema,I as CustomerState$outboundSchema,J as SDKValidationError,P as Polar}from"./sdk_DEQ9AU5A.mjs";var hasRequiredTiming_safe_equal,handleWebhookPayload=async(e,{webhookSecret:t,entitlements:o,onPayload:a,...n})=>{const r=[];switch(a&&r.push(a(e)),e.type){case"checkout.created":n.onCheckoutCreated&&r.push(n.onCheckoutCreated(e));break;case"checkout.updated":n.onCheckoutUpdated&&r.push(n.onCheckoutUpdated(e));break;case"order.created":n.onOrderCreated&&r.push(n.onOrderCreated(e));break;case"order.updated":n.onOrderUpdated&&r.push(n.onOrderUpdated(e));break;case"order.paid":n.onOrderPaid&&r.push(n.onOrderPaid(e));break;case"subscription.created":n.onSubscriptionCreated&&r.push(n.onSubscriptionCreated(e));break;case"subscription.updated":n.onSubscriptionUpdated&&r.push(n.onSubscriptionUpdated(e));break;case"subscription.active":n.onSubscriptionActive&&r.push(n.onSubscriptionActive(e));break;case"subscription.canceled":n.onSubscriptionCanceled&&r.push(n.onSubscriptionCanceled(e));break;case"subscription.revoked":n.onSubscriptionRevoked&&r.push(n.onSubscriptionRevoked(e));break;case"product.created":n.onProductCreated&&r.push(n.onProductCreated(e));break;case"product.updated":n.onProductUpdated&&r.push(n.onProductUpdated(e));break;case"organization.updated":n.onOrganizationUpdated&&r.push(n.onOrganizationUpdated(e));break;case"benefit.created":n.onBenefitCreated&&r.push(n.onBenefitCreated(e));break;case"benefit.updated":n.onBenefitUpdated&&r.push(n.onBenefitUpdated(e));break;case"benefit_grant.created":n.onBenefitGrantCreated&&r.push(n.onBenefitGrantCreated(e));break;case"benefit_grant.updated":n.onBenefitGrantUpdated&&r.push(n.onBenefitGrantUpdated(e));break;case"benefit_grant.revoked":n.onBenefitGrantRevoked&&r.push(n.onBenefitGrantRevoked(e));break;case"customer.created":n.onCustomerCreated&&r.push(n.onCustomerCreated(e));break;case"customer.updated":n.onCustomerUpdated&&r.push(n.onCustomerUpdated(e));break;case"customer.deleted":n.onCustomerDeleted&&r.push(n.onCustomerDeleted(e));break;case"customer.state_changed":n.onCustomerStateChanged&&r.push(n.onCustomerStateChanged(e))}switch(e.type){case"benefit_grant.created":case"benefit_grant.revoked":if(o)for(const t of o.handlers)r.push(t(e))}return Promise.all(r)},dist={},timing_safe_equal={};function requireTiming_safe_equal(){if(hasRequiredTiming_safe_equal)return timing_safe_equal;function e(e,t=""){if(!e)throw new Error(t)}return hasRequiredTiming_safe_equal=1,Object.defineProperty(timing_safe_equal,"__esModule",{value:!0}),timing_safe_equal.timingSafeEqual=void 0,timing_safe_equal.timingSafeEqual=function(t,o){if(t.byteLength!==o.byteLength)return!1;t instanceof DataView||(t=new DataView(ArrayBuffer.isView(t)?t.buffer:t)),o instanceof DataView||(o=new DataView(ArrayBuffer.isView(o)?o.buffer:o)),e(t instanceof DataView),e(o instanceof DataView);const a=t.byteLength;let n=0,r=-1;for(;++r<a;)n|=t.getUint8(r)^o.getUint8(r);return 0===n},timing_safe_equal}var hasRequiredBase64,base64={};function requireBase64(){if(hasRequiredBase64)return base64;hasRequiredBase64=1;var e,t=base64&&base64.__extends||(e=function(t,o){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)t.hasOwnProperty(o)&&(e[o]=t[o])},e(t,o)},function(t,o){function a(){this.constructor=t}e(t,o),t.prototype=null===o?Object.create(o):(a.prototype=o.prototype,new a)});Object.defineProperty(base64,"__esModule",{value:!0});var o=256,a=function(){function e(e){void 0===e&&(e="="),this._paddingCharacter=e}return e.prototype.encodedLength=function(e){return this._paddingCharacter?(e+2)/3*4|0:(8*e+5)/6|0},e.prototype.encode=function(e){for(var t="",o=0;o<e.length-2;o+=3){var a=e[o]<<16|e[o+1]<<8|e[o+2];t+=this._encodeByte(a>>>18&63),t+=this._encodeByte(a>>>12&63),t+=this._encodeByte(a>>>6&63),t+=this._encodeByte(a>>>0&63)}var n=e.length-o;if(n>0){a=e[o]<<16|(2===n?e[o+1]<<8:0);t+=this._encodeByte(a>>>18&63),t+=this._encodeByte(a>>>12&63),t+=2===n?this._encodeByte(a>>>6&63):this._paddingCharacter||"",t+=this._paddingCharacter||""}return t},e.prototype.maxDecodedLength=function(e){return this._paddingCharacter?e/4*3|0:(6*e+7)/8|0},e.prototype.decodedLength=function(e){return this.maxDecodedLength(e.length-this._getPaddingLength(e))},e.prototype.decode=function(e){if(0===e.length)return new Uint8Array(0);for(var t=this._getPaddingLength(e),a=e.length-t,n=new Uint8Array(this.maxDecodedLength(a)),r=0,d=0,i=0,u=0,s=0,c=0,h=0;d<a-4;d+=4)u=this._decodeChar(e.charCodeAt(d+0)),s=this._decodeChar(e.charCodeAt(d+1)),c=this._decodeChar(e.charCodeAt(d+2)),h=this._decodeChar(e.charCodeAt(d+3)),n[r++]=u<<2|s>>>4,n[r++]=s<<4|c>>>2,n[r++]=c<<6|h,i|=u&o,i|=s&o,i|=c&o,i|=h&o;if(d<a-1&&(u=this._decodeChar(e.charCodeAt(d)),s=this._decodeChar(e.charCodeAt(d+1)),n[r++]=u<<2|s>>>4,i|=u&o,i|=s&o),d<a-2&&(c=this._decodeChar(e.charCodeAt(d+2)),n[r++]=s<<4|c>>>2,i|=c&o),d<a-3&&(h=this._decodeChar(e.charCodeAt(d+3)),n[r++]=c<<6|h,i|=h&o),0!==i)throw new Error("Base64Coder: incorrect characters for decoding");return n},e.prototype._encodeByte=function(e){var t=e;return t+=65,t+=25-e>>>8&6,t+=51-e>>>8&-75,t+=61-e>>>8&-15,t+=62-e>>>8&3,String.fromCharCode(t)},e.prototype._decodeChar=function(e){var t=o;return t+=(42-e&e-44)>>>8&-256+e-43+62,t+=(46-e&e-48)>>>8&-256+e-47+63,t+=(47-e&e-58)>>>8&-256+e-48+52,t+=(64-e&e-91)>>>8&-256+e-65+0,t+=(96-e&e-123)>>>8&-256+e-97+26},e.prototype._getPaddingLength=function(e){var t=0;if(this._paddingCharacter){for(var o=e.length-1;o>=0&&e[o]===this._paddingCharacter;o--)t++;if(e.length<4||t>2)throw new Error("Base64Coder: incorrect padding")}return t},e}();base64.Coder=a;var n=new a;base64.encode=function(e){return n.encode(e)},base64.decode=function(e){return n.decode(e)};var r=function(e){function a(){return null!==e&&e.apply(this,arguments)||this}return t(a,e),a.prototype._encodeByte=function(e){var t=e;return t+=65,t+=25-e>>>8&6,t+=51-e>>>8&-75,t+=61-e>>>8&-13,t+=62-e>>>8&49,String.fromCharCode(t)},a.prototype._decodeChar=function(e){var t=o;return t+=(44-e&e-46)>>>8&-256+e-45+62,t+=(94-e&e-96)>>>8&-256+e-95+63,t+=(47-e&e-58)>>>8&-256+e-48+52,t+=(64-e&e-91)>>>8&-256+e-65+0,t+=(96-e&e-123)>>>8&-256+e-97+26},a}(a);base64.URLSafeCoder=r;var d=new r;return base64.encodeURLSafe=function(e){return d.encode(e)},base64.decodeURLSafe=function(e){return d.decode(e)},base64.encodedLength=function(e){return n.encodedLength(e)},base64.maxDecodedLength=function(e){return n.maxDecodedLength(e)},base64.decodedLength=function(e){return n.decodedLength(e)},base64}var hasRequiredSha256,hasRequiredDist,sha256$1={exports:{}},sha256=sha256$1.exports;function requireSha256(){return hasRequiredSha256||(hasRequiredSha256=1,e=sha256$1,function(){var t={};!function(e){e.__esModule=!0,e.digestLength=32,e.blockSize=64;var t=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);function o(e,o,a,n,r){for(var d,i,u,s,c,h,b,p,l,y,m,f,S;r>=64;){for(d=o[0],i=o[1],u=o[2],s=o[3],c=o[4],h=o[5],b=o[6],p=o[7],y=0;y<16;y++)m=n+4*y,e[y]=(255&a[m])<<24|(255&a[m+1])<<16|(255&a[m+2])<<8|255&a[m+3];for(y=16;y<64;y++)f=((l=e[y-2])>>>17|l<<15)^(l>>>19|l<<13)^l>>>10,S=((l=e[y-15])>>>7|l<<25)^(l>>>18|l<<14)^l>>>3,e[y]=(f+e[y-7]|0)+(S+e[y-16]|0);for(y=0;y<64;y++)f=(((c>>>6|c<<26)^(c>>>11|c<<21)^(c>>>25|c<<7))+(c&h^~c&b)|0)+(p+(t[y]+e[y]|0)|0)|0,S=((d>>>2|d<<30)^(d>>>13|d<<19)^(d>>>22|d<<10))+(d&i^d&u^i&u)|0,p=b,b=h,h=c,c=s+f|0,s=u,u=i,i=d,d=f+S|0;o[0]+=d,o[1]+=i,o[2]+=u,o[3]+=s,o[4]+=c,o[5]+=h,o[6]+=b,o[7]+=p,n+=64,r-=64}return n}var a=function(){function t(){this.digestLength=e.digestLength,this.blockSize=e.blockSize,this.state=new Int32Array(8),this.temp=new Int32Array(64),this.buffer=new Uint8Array(128),this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this.reset()}return t.prototype.reset=function(){return this.state[0]=1779033703,this.state[1]=3144134277,this.state[2]=1013904242,this.state[3]=2773480762,this.state[4]=1359893119,this.state[5]=2600822924,this.state[6]=528734635,this.state[7]=1541459225,this.bufferLength=0,this.bytesHashed=0,this.finished=!1,this},t.prototype.clean=function(){for(var e=0;e<this.buffer.length;e++)this.buffer[e]=0;for(e=0;e<this.temp.length;e++)this.temp[e]=0;this.reset()},t.prototype.update=function(e,t){if(void 0===t&&(t=e.length),this.finished)throw new Error("SHA256: can't update because hash was finished.");var a=0;if(this.bytesHashed+=t,this.bufferLength>0){for(;this.bufferLength<64&&t>0;)this.buffer[this.bufferLength++]=e[a++],t--;64===this.bufferLength&&(o(this.temp,this.state,this.buffer,0,64),this.bufferLength=0)}for(t>=64&&(a=o(this.temp,this.state,e,a,t),t%=64);t>0;)this.buffer[this.bufferLength++]=e[a++],t--;return this},t.prototype.finish=function(e){if(!this.finished){var t=this.bytesHashed,a=this.bufferLength,n=t/536870912|0,r=t<<3,d=t%64<56?64:128;this.buffer[a]=128;for(var i=a+1;i<d-8;i++)this.buffer[i]=0;this.buffer[d-8]=n>>>24&255,this.buffer[d-7]=n>>>16&255,this.buffer[d-6]=n>>>8&255,this.buffer[d-5]=n>>>0&255,this.buffer[d-4]=r>>>24&255,this.buffer[d-3]=r>>>16&255,this.buffer[d-2]=r>>>8&255,this.buffer[d-1]=r>>>0&255,o(this.temp,this.state,this.buffer,0,d),this.finished=!0}for(i=0;i<8;i++)e[4*i+0]=this.state[i]>>>24&255,e[4*i+1]=this.state[i]>>>16&255,e[4*i+2]=this.state[i]>>>8&255,e[4*i+3]=this.state[i]>>>0&255;return this},t.prototype.digest=function(){var e=new Uint8Array(this.digestLength);return this.finish(e),e},t.prototype._saveState=function(e){for(var t=0;t<this.state.length;t++)e[t]=this.state[t]},t.prototype._restoreState=function(e,t){for(var o=0;o<this.state.length;o++)this.state[o]=e[o];this.bytesHashed=t,this.finished=!1,this.bufferLength=0},t}();e.Hash=a;var n=function(){function e(e){this.inner=new a,this.outer=new a,this.blockSize=this.inner.blockSize,this.digestLength=this.inner.digestLength;var t=new Uint8Array(this.blockSize);if(e.length>this.blockSize)(new a).update(e).finish(t).clean();else for(var o=0;o<e.length;o++)t[o]=e[o];for(o=0;o<t.length;o++)t[o]^=54;for(this.inner.update(t),o=0;o<t.length;o++)t[o]^=106;for(this.outer.update(t),this.istate=new Uint32Array(8),this.ostate=new Uint32Array(8),this.inner._saveState(this.istate),this.outer._saveState(this.ostate),o=0;o<t.length;o++)t[o]=0}return e.prototype.reset=function(){return this.inner._restoreState(this.istate,this.inner.blockSize),this.outer._restoreState(this.ostate,this.outer.blockSize),this},e.prototype.clean=function(){for(var e=0;e<this.istate.length;e++)this.ostate[e]=this.istate[e]=0;this.inner.clean(),this.outer.clean()},e.prototype.update=function(e){return this.inner.update(e),this},e.prototype.finish=function(e){return this.outer.finished?this.outer.finish(e):(this.inner.finish(e),this.outer.update(e,this.digestLength).finish(e)),this},e.prototype.digest=function(){var e=new Uint8Array(this.digestLength);return this.finish(e),e},e}();function r(e){var t=(new a).update(e),o=t.digest();return t.clean(),o}function d(e,t){var o=new n(e).update(t),a=o.digest();return o.clean(),a}function i(e,t,o,a){var n=a[0];if(0===n)throw new Error("hkdf: cannot expand more");t.reset(),n>1&&t.update(e),o&&t.update(o),t.update(a),t.finish(e),a[0]++}e.HMAC=n,e.hash=r,e.default=r,e.hmac=d;var u=new Uint8Array(e.digestLength);function s(e,t,o,a){void 0===t&&(t=u),void 0===a&&(a=32);for(var r=new Uint8Array([1]),s=d(t,e),c=new n(s),h=new Uint8Array(c.digestLength),b=h.length,p=new Uint8Array(a),l=0;l<a;l++)b===h.length&&(i(h,c,o,r),b=0),p[l]=h[b++];return c.clean(),h.fill(0),r.fill(0),p}function c(e,t,o,a){for(var r=new n(e),d=r.digestLength,i=new Uint8Array(4),u=new Uint8Array(d),s=new Uint8Array(d),c=new Uint8Array(a),h=0;h*d<a;h++){var b=h+1;i[0]=b>>>24&255,i[1]=b>>>16&255,i[2]=b>>>8&255,i[3]=b>>>0&255,r.reset(),r.update(t),r.update(i),r.finish(s);for(var p=0;p<d;p++)u[p]=s[p];for(p=2;p<=o;p++){r.reset(),r.update(s).finish(s);for(var l=0;l<d;l++)u[l]^=s[l]}for(p=0;p<d&&h*d+p<a;p++)c[h*d+p]=u[p]}for(h=0;h<d;h++)u[h]=s[h]=0;for(h=0;h<4;h++)i[h]=0;return r.clean(),c}e.hkdf=s,e.pbkdf2=c}(t);var o=t.default;for(var a in t)o[a]=t[a];e.exports=o}()),sha256$1.exports;var e}function requireDist(){if(hasRequiredDist)return dist;hasRequiredDist=1,Object.defineProperty(dist,"__esModule",{value:!0}),dist.Webhook=dist.WebhookVerificationError=void 0;const e=requireTiming_safe_equal(),t=requireBase64(),o=requireSha256();class a extends Error{constructor(e){super(e),Object.setPrototypeOf(this,a.prototype),this.name="ExtendableError",this.stack=new Error(e).stack}}class n extends a{constructor(e){super(e),Object.setPrototypeOf(this,n.prototype),this.name="WebhookVerificationError"}}dist.WebhookVerificationError=n;class r{constructor(e,o){if(!e)throw new Error("Secret can't be empty.");if("raw"===(null==o?void 0:o.format))e instanceof Uint8Array?this.key=e:this.key=Uint8Array.from(e,(e=>e.charCodeAt(0)));else{if("string"!=typeof e)throw new Error("Expected secret to be of type string");e.startsWith(r.prefix)&&(e=e.substring(r.prefix.length)),this.key=t.decode(e)}}verify(t,o){const a={};for(const e of Object.keys(o))a[e.toLowerCase()]=o[e];const r=a["webhook-id"],d=a["webhook-signature"],i=a["webhook-timestamp"];if(!d||!r||!i)throw new n("Missing required headers");const u=this.verifyTimestamp(i),s=this.sign(r,u,t).split(",")[1],c=d.split(" "),h=new globalThis.TextEncoder;for(const o of c){const[a,n]=o.split(",");if("v1"===a&&(0,e.timingSafeEqual)(h.encode(n),h.encode(s)))return JSON.parse(t.toString())}throw new n("No matching signature found")}sign(e,a,n){if("string"==typeof n);else{if("Buffer"!==n.constructor.name)throw new Error("Expected payload to be of type string or Buffer.");n=n.toString()}const r=new TextEncoder,d=Math.floor(a.getTime()/1e3),i=r.encode(`${e}.${d}.${n}`);return`v1,${t.encode(o.hmac(this.key,i))}`}verifyTimestamp(e){const t=Math.floor(Date.now()/1e3),o=parseInt(e,10);if(isNaN(o))throw new n("Invalid Signature Headers");if(t-o>300)throw new n("Message timestamp too old");if(o>t+300)throw new n("Message timestamp too new");return new Date(1e3*o)}}return dist.Webhook=r,r.prefix="whsec_",dist}var distExports=requireDist();const WebhookBenefitCreatedPayload$inboundSchema=objectType({type:literalType("benefit.created"),data:Benefit$inboundSchema}),WebhookBenefitCreatedPayload$outboundSchema=objectType({type:literalType("benefit.created"),data:Benefit$outboundSchema});var WebhookBenefitCreatedPayload$;!function(e){e.inboundSchema=WebhookBenefitCreatedPayload$inboundSchema,e.outboundSchema=WebhookBenefitCreatedPayload$outboundSchema}(WebhookBenefitCreatedPayload$||(WebhookBenefitCreatedPayload$={}));const BenefitGrantWebhookProperties$inboundSchema=unionType([BenefitGrantDiscordProperties$inboundSchema,BenefitGrantGitHubRepositoryProperties$inboundSchema,BenefitGrantDownloadablesProperties$inboundSchema,BenefitGrantLicenseKeysProperties$inboundSchema,BenefitGrantCustomProperties$inboundSchema]),BenefitGrantWebhookProperties$outboundSchema=unionType([BenefitGrantDiscordProperties$outboundSchema,BenefitGrantGitHubRepositoryProperties$outboundSchema,BenefitGrantDownloadablesProperties$outboundSchema,BenefitGrantLicenseKeysProperties$outboundSchema,BenefitGrantCustomProperties$outboundSchema]);var BenefitGrantWebhookProperties$;!function(e){e.inboundSchema=BenefitGrantWebhookProperties$inboundSchema,e.outboundSchema=BenefitGrantWebhookProperties$outboundSchema}(BenefitGrantWebhookProperties$||(BenefitGrantWebhookProperties$={}));const PreviousProperties$inboundSchema=unionType([BenefitGrantDiscordProperties$inboundSchema,BenefitGrantGitHubRepositoryProperties$inboundSchema,BenefitGrantDownloadablesProperties$inboundSchema,BenefitGrantLicenseKeysProperties$inboundSchema,BenefitGrantCustomProperties$inboundSchema]),PreviousProperties$outboundSchema=unionType([BenefitGrantDiscordProperties$outboundSchema,BenefitGrantGitHubRepositoryProperties$outboundSchema,BenefitGrantDownloadablesProperties$outboundSchema,BenefitGrantLicenseKeysProperties$outboundSchema,BenefitGrantCustomProperties$outboundSchema]);var PreviousProperties$;!function(e){e.inboundSchema=PreviousProperties$inboundSchema,e.outboundSchema=PreviousProperties$outboundSchema}(PreviousProperties$||(PreviousProperties$={}));const BenefitGrantWebhook$inboundSchema=objectType({created_at:stringType().datetime({offset:!0}).transform((e=>new Date(e))),modified_at:nullableType(stringType().datetime({offset:!0}).transform((e=>new Date(e)))),id:stringType(),granted_at:nullableType(stringType().datetime({offset:!0}).transform((e=>new Date(e)))).optional(),is_granted:booleanType(),revoked_at:nullableType(stringType().datetime({offset:!0}).transform((e=>new Date(e)))).optional(),is_revoked:booleanType(),subscription_id:nullableType(stringType()),order_id:nullableType(stringType()),customer_id:stringType(),benefit_id:stringType(),error:nullableType(BenefitGrantError$inboundSchema).optional(),customer:Customer$inboundSchema,properties:unionType([BenefitGrantDiscordProperties$inboundSchema,BenefitGrantGitHubRepositoryProperties$inboundSchema,BenefitGrantDownloadablesProperties$inboundSchema,BenefitGrantLicenseKeysProperties$inboundSchema,BenefitGrantCustomProperties$inboundSchema]),benefit:Benefit$inboundSchema,previous_properties:nullableType(unionType([BenefitGrantDiscordProperties$inboundSchema,BenefitGrantGitHubRepositoryProperties$inboundSchema,BenefitGrantDownloadablesProperties$inboundSchema,BenefitGrantLicenseKeysProperties$inboundSchema,BenefitGrantCustomProperties$inboundSchema])).optional()}).transform((e=>remap(e,{created_at:"createdAt",modified_at:"modifiedAt",granted_at:"grantedAt",is_granted:"isGranted",revoked_at:"revokedAt",is_revoked:"isRevoked",subscription_id:"subscriptionId",order_id:"orderId",customer_id:"customerId",benefit_id:"benefitId",previous_properties:"previousProperties"}))),BenefitGrantWebhook$outboundSchema=objectType({createdAt:dateType().transform((e=>e.toISOString())),modifiedAt:nullableType(dateType().transform((e=>e.toISOString()))),id:stringType(),grantedAt:nullableType(dateType().transform((e=>e.toISOString()))).optional(),isGranted:booleanType(),revokedAt:nullableType(dateType().transform((e=>e.toISOString()))).optional(),isRevoked:booleanType(),subscriptionId:nullableType(stringType()),orderId:nullableType(stringType()),customerId:stringType(),benefitId:stringType(),error:nullableType(BenefitGrantError$outboundSchema).optional(),customer:Customer$outboundSchema,properties:unionType([BenefitGrantDiscordProperties$outboundSchema,BenefitGrantGitHubRepositoryProperties$outboundSchema,BenefitGrantDownloadablesProperties$outboundSchema,BenefitGrantLicenseKeysProperties$outboundSchema,BenefitGrantCustomProperties$outboundSchema]),benefit:Benefit$outboundSchema,previousProperties:nullableType(unionType([BenefitGrantDiscordProperties$outboundSchema,BenefitGrantGitHubRepositoryProperties$outboundSchema,BenefitGrantDownloadablesProperties$outboundSchema,BenefitGrantLicenseKeysProperties$outboundSchema,BenefitGrantCustomProperties$outboundSchema])).optional()}).transform((e=>remap(e,{createdAt:"created_at",modifiedAt:"modified_at",grantedAt:"granted_at",isGranted:"is_granted",revokedAt:"revoked_at",isRevoked:"is_revoked",subscriptionId:"subscription_id",orderId:"order_id",customerId:"customer_id",benefitId:"benefit_id",previousProperties:"previous_properties"})));var BenefitGrantWebhook$;!function(e){e.inboundSchema=BenefitGrantWebhook$inboundSchema,e.outboundSchema=BenefitGrantWebhook$outboundSchema}(BenefitGrantWebhook$||(BenefitGrantWebhook$={}));const WebhookBenefitGrantCreatedPayload$inboundSchema=objectType({type:literalType("benefit_grant.created"),data:BenefitGrantWebhook$inboundSchema}),WebhookBenefitGrantCreatedPayload$outboundSchema=objectType({type:literalType("benefit_grant.created"),data:BenefitGrantWebhook$outboundSchema});var WebhookBenefitGrantCreatedPayload$;!function(e){e.inboundSchema=WebhookBenefitGrantCreatedPayload$inboundSchema,e.outboundSchema=WebhookBenefitGrantCreatedPayload$outboundSchema}(WebhookBenefitGrantCreatedPayload$||(WebhookBenefitGrantCreatedPayload$={}));const WebhookBenefitGrantRevokedPayload$inboundSchema=objectType({type:literalType("benefit_grant.revoked"),data:BenefitGrantWebhook$inboundSchema}),WebhookBenefitGrantRevokedPayload$outboundSchema=objectType({type:literalType("benefit_grant.revoked"),data:BenefitGrantWebhook$outboundSchema});var WebhookBenefitGrantRevokedPayload$;!function(e){e.inboundSchema=WebhookBenefitGrantRevokedPayload$inboundSchema,e.outboundSchema=WebhookBenefitGrantRevokedPayload$outboundSchema}(WebhookBenefitGrantRevokedPayload$||(WebhookBenefitGrantRevokedPayload$={}));const WebhookBenefitGrantUpdatedPayload$inboundSchema=objectType({type:literalType("benefit_grant.updated"),data:BenefitGrantWebhook$inboundSchema}),WebhookBenefitGrantUpdatedPayload$outboundSchema=objectType({type:literalType("benefit_grant.updated"),data:BenefitGrantWebhook$outboundSchema});var WebhookBenefitGrantUpdatedPayload$;!function(e){e.inboundSchema=WebhookBenefitGrantUpdatedPayload$inboundSchema,e.outboundSchema=WebhookBenefitGrantUpdatedPayload$outboundSchema}(WebhookBenefitGrantUpdatedPayload$||(WebhookBenefitGrantUpdatedPayload$={}));const WebhookBenefitGrantCycledPayload$inboundSchema=objectType({type:literalType("benefit_grant.cycled"),data:BenefitGrantWebhook$inboundSchema}),WebhookBenefitGrantCycledPayload$outboundSchema=objectType({type:literalType("benefit_grant.cycled"),data:BenefitGrantWebhook$outboundSchema});var WebhookBenefitGrantCycledPayload$;!function(e){e.inboundSchema=WebhookBenefitGrantCycledPayload$inboundSchema,e.outboundSchema=WebhookBenefitGrantCycledPayload$outboundSchema}(WebhookBenefitGrantCycledPayload$||(WebhookBenefitGrantCycledPayload$={}));const WebhookBenefitUpdatedPayload$inboundSchema=objectType({type:literalType("benefit.updated"),data:Benefit$inboundSchema}),WebhookBenefitUpdatedPayload$outboundSchema=objectType({type:literalType("benefit.updated"),data:Benefit$outboundSchema});var WebhookBenefitUpdatedPayload$;!function(e){e.inboundSchema=WebhookBenefitUpdatedPayload$inboundSchema,e.outboundSchema=WebhookBenefitUpdatedPayload$outboundSchema}(WebhookBenefitUpdatedPayload$||(WebhookBenefitUpdatedPayload$={}));const WebhookCheckoutCreatedPayload$inboundSchema=objectType({type:literalType("checkout.created"),data:Checkout$inboundSchema}),WebhookCheckoutCreatedPayload$outboundSchema=objectType({type:literalType("checkout.created"),data:Checkout$outboundSchema});var WebhookCheckoutCreatedPayload$;!function(e){e.inboundSchema=WebhookCheckoutCreatedPayload$inboundSchema,e.outboundSchema=WebhookCheckoutCreatedPayload$outboundSchema}(WebhookCheckoutCreatedPayload$||(WebhookCheckoutCreatedPayload$={}));const WebhookCheckoutUpdatedPayload$inboundSchema=objectType({type:literalType("checkout.updated"),data:Checkout$inboundSchema}),WebhookCheckoutUpdatedPayload$outboundSchema=objectType({type:literalType("checkout.updated"),data:Checkout$outboundSchema});var WebhookCheckoutUpdatedPayload$;!function(e){e.inboundSchema=WebhookCheckoutUpdatedPayload$inboundSchema,e.outboundSchema=WebhookCheckoutUpdatedPayload$outboundSchema}(WebhookCheckoutUpdatedPayload$||(WebhookCheckoutUpdatedPayload$={}));const WebhookOrderCreatedPayload$inboundSchema=objectType({type:literalType("order.created"),data:Order$inboundSchema}),WebhookOrderCreatedPayload$outboundSchema=objectType({type:literalType("order.created"),data:Order$outboundSchema});var WebhookOrderCreatedPayload$;!function(e){e.inboundSchema=WebhookOrderCreatedPayload$inboundSchema,e.outboundSchema=WebhookOrderCreatedPayload$outboundSchema}(WebhookOrderCreatedPayload$||(WebhookOrderCreatedPayload$={}));const WebhookOrderRefundedPayload$inboundSchema=objectType({type:literalType("order.refunded"),data:Order$inboundSchema}),WebhookOrderRefundedPayload$outboundSchema=objectType({type:literalType("order.refunded"),data:Order$outboundSchema});var WebhookOrderRefundedPayload$;!function(e){e.inboundSchema=WebhookOrderRefundedPayload$inboundSchema,e.outboundSchema=WebhookOrderRefundedPayload$outboundSchema}(WebhookOrderRefundedPayload$||(WebhookOrderRefundedPayload$={}));const WebhookOrderUpdatedPayload$inboundSchema=objectType({type:literalType("order.updated"),data:Order$inboundSchema}),WebhookOrderUpdatedPayload$outboundSchema=objectType({type:literalType("order.updated"),data:Order$outboundSchema});var WebhookOrderUpdatedPayload$;!function(e){e.inboundSchema=WebhookOrderUpdatedPayload$inboundSchema,e.outboundSchema=WebhookOrderUpdatedPayload$outboundSchema}(WebhookOrderUpdatedPayload$||(WebhookOrderUpdatedPayload$={}));const WebhookOrderPaidPayload$inboundSchema=objectType({type:literalType("order.paid"),data:Order$inboundSchema}),WebhookOrderPaidPayload$outboundSchema=objectType({type:literalType("order.paid"),data:Order$outboundSchema});var WebhookOrderPaidPayload$;!function(e){e.inboundSchema=WebhookOrderPaidPayload$inboundSchema,e.outboundSchema=WebhookOrderPaidPayload$outboundSchema}(WebhookOrderPaidPayload$||(WebhookOrderPaidPayload$={}));const WebhookOrganizationUpdatedPayload$inboundSchema=objectType({type:literalType("organization.updated"),data:Organization$inboundSchema}),WebhookOrganizationUpdatedPayload$outboundSchema=objectType({type:literalType("organization.updated"),data:Organization$outboundSchema});var WebhookOrganizationUpdatedPayload$;!function(e){e.inboundSchema=WebhookOrganizationUpdatedPayload$inboundSchema,e.outboundSchema=WebhookOrganizationUpdatedPayload$outboundSchema}(WebhookOrganizationUpdatedPayload$||(WebhookOrganizationUpdatedPayload$={}));const WebhookProductCreatedPayload$inboundSchema=objectType({type:literalType("product.created"),data:Product$inboundSchema}),WebhookProductCreatedPayload$outboundSchema=objectType({type:literalType("product.created"),data:Product$outboundSchema});var WebhookProductCreatedPayload$;!function(e){e.inboundSchema=WebhookProductCreatedPayload$inboundSchema,e.outboundSchema=WebhookProductCreatedPayload$outboundSchema}(WebhookProductCreatedPayload$||(WebhookProductCreatedPayload$={}));const WebhookProductUpdatedPayload$inboundSchema=objectType({type:literalType("product.updated"),data:Product$inboundSchema}),WebhookProductUpdatedPayload$outboundSchema=objectType({type:literalType("product.updated"),data:Product$outboundSchema});var WebhookProductUpdatedPayload$;!function(e){e.inboundSchema=WebhookProductUpdatedPayload$inboundSchema,e.outboundSchema=WebhookProductUpdatedPayload$outboundSchema}(WebhookProductUpdatedPayload$||(WebhookProductUpdatedPayload$={}));const WebhookRefundCreatedPayload$inboundSchema=objectType({type:literalType("refund.created"),data:Refund$inboundSchema}),WebhookRefundCreatedPayload$outboundSchema=objectType({type:literalType("refund.created"),data:Refund$outboundSchema});var WebhookRefundCreatedPayload$;!function(e){e.inboundSchema=WebhookRefundCreatedPayload$inboundSchema,e.outboundSchema=WebhookRefundCreatedPayload$outboundSchema}(WebhookRefundCreatedPayload$||(WebhookRefundCreatedPayload$={}));const WebhookRefundUpdatedPayload$inboundSchema=objectType({type:literalType("refund.updated"),data:Refund$inboundSchema}),WebhookRefundUpdatedPayload$outboundSchema=objectType({type:literalType("refund.updated"),data:Refund$outboundSchema});var WebhookRefundUpdatedPayload$;!function(e){e.inboundSchema=WebhookRefundUpdatedPayload$inboundSchema,e.outboundSchema=WebhookRefundUpdatedPayload$outboundSchema}(WebhookRefundUpdatedPayload$||(WebhookRefundUpdatedPayload$={}));const WebhookSubscriptionActivePayload$inboundSchema=objectType({type:literalType("subscription.active"),data:Subscription$inboundSchema}),WebhookSubscriptionActivePayload$outboundSchema=objectType({type:literalType("subscription.active"),data:Subscription$outboundSchema});var WebhookSubscriptionActivePayload$;!function(e){e.inboundSchema=WebhookSubscriptionActivePayload$inboundSchema,e.outboundSchema=WebhookSubscriptionActivePayload$outboundSchema}(WebhookSubscriptionActivePayload$||(WebhookSubscriptionActivePayload$={}));const WebhookSubscriptionCanceledPayload$inboundSchema=objectType({type:literalType("subscription.canceled"),data:Subscription$inboundSchema}),WebhookSubscriptionCanceledPayload$outboundSchema=objectType({type:literalType("subscription.canceled"),data:Subscription$outboundSchema});var WebhookSubscriptionCanceledPayload$;!function(e){e.inboundSchema=WebhookSubscriptionCanceledPayload$inboundSchema,e.outboundSchema=WebhookSubscriptionCanceledPayload$outboundSchema}(WebhookSubscriptionCanceledPayload$||(WebhookSubscriptionCanceledPayload$={}));const WebhookSubscriptionCreatedPayload$inboundSchema=objectType({type:literalType("subscription.created"),data:Subscription$inboundSchema}),WebhookSubscriptionCreatedPayload$outboundSchema=objectType({type:literalType("subscription.created"),data:Subscription$outboundSchema});var WebhookSubscriptionCreatedPayload$;!function(e){e.inboundSchema=WebhookSubscriptionCreatedPayload$inboundSchema,e.outboundSchema=WebhookSubscriptionCreatedPayload$outboundSchema}(WebhookSubscriptionCreatedPayload$||(WebhookSubscriptionCreatedPayload$={}));const WebhookSubscriptionRevokedPayload$inboundSchema=objectType({type:literalType("subscription.revoked"),data:Subscription$inboundSchema}),WebhookSubscriptionRevokedPayload$outboundSchema=objectType({type:literalType("subscription.revoked"),data:Subscription$outboundSchema});var WebhookSubscriptionRevokedPayload$;!function(e){e.inboundSchema=WebhookSubscriptionRevokedPayload$inboundSchema,e.outboundSchema=WebhookSubscriptionRevokedPayload$outboundSchema}(WebhookSubscriptionRevokedPayload$||(WebhookSubscriptionRevokedPayload$={}));const WebhookSubscriptionUncanceledPayload$inboundSchema=objectType({type:literalType("subscription.uncanceled"),data:Subscription$inboundSchema}),WebhookSubscriptionUncanceledPayload$outboundSchema=objectType({type:literalType("subscription.uncanceled"),data:Subscription$outboundSchema});var WebhookSubscriptionUncanceledPayload$;!function(e){e.inboundSchema=WebhookSubscriptionUncanceledPayload$inboundSchema,e.outboundSchema=WebhookSubscriptionUncanceledPayload$outboundSchema}(WebhookSubscriptionUncanceledPayload$||(WebhookSubscriptionUncanceledPayload$={}));const WebhookSubscriptionUpdatedPayload$inboundSchema=objectType({type:literalType("subscription.updated"),data:Subscription$inboundSchema}),WebhookSubscriptionUpdatedPayload$outboundSchema=objectType({type:literalType("subscription.updated"),data:Subscription$outboundSchema});var WebhookSubscriptionUpdatedPayload$;!function(e){e.inboundSchema=WebhookSubscriptionUpdatedPayload$inboundSchema,e.outboundSchema=WebhookSubscriptionUpdatedPayload$outboundSchema}(WebhookSubscriptionUpdatedPayload$||(WebhookSubscriptionUpdatedPayload$={}));const WebhookCustomerCreatedPayload$inboundSchema=objectType({type:literalType("customer.created"),data:Customer$inboundSchema}),WebhookCustomerCreatedPayload$outboundSchema=objectType({type:literalType("customer.created"),data:Customer$outboundSchema});var WebhookCustomerCreatedPayload$;!function(e){e.inboundSchema=WebhookCustomerCreatedPayload$inboundSchema,e.outboundSchema=WebhookCustomerCreatedPayload$outboundSchema}(WebhookCustomerCreatedPayload$||(WebhookCustomerCreatedPayload$={}));const WebhookCustomerUpdatedPayload$inboundSchema=objectType({type:literalType("customer.updated"),data:Customer$inboundSchema}),WebhookCustomerUpdatedPayload$outboundSchema=objectType({type:literalType("customer.updated"),data:Customer$outboundSchema});var WebhookCustomerUpdatedPayload$;!function(e){e.inboundSchema=WebhookCustomerUpdatedPayload$inboundSchema,e.outboundSchema=WebhookCustomerUpdatedPayload$outboundSchema}(WebhookCustomerUpdatedPayload$||(WebhookCustomerUpdatedPayload$={}));const WebhookCustomerDeletedPayload$inboundSchema=objectType({type:literalType("customer.deleted"),data:Customer$inboundSchema}),WebhookCustomerDeletedPayload$outboundSchema=objectType({type:literalType("customer.deleted"),data:Customer$outboundSchema});var WebhookCustomerDeletedPayload$;!function(e){e.inboundSchema=WebhookCustomerDeletedPayload$inboundSchema,e.outboundSchema=WebhookCustomerDeletedPayload$outboundSchema}(WebhookCustomerDeletedPayload$||(WebhookCustomerDeletedPayload$={}));const WebhookCustomerStateChangedPayload$inboundSchema=objectType({type:literalType("customer.state_changed"),data:CustomerState$inboundSchema}),WebhookCustomerStateChangedPayload$outboundSchema=objectType({type:literalType("customer.state_changed"),data:CustomerState$outboundSchema});var WebhookCustomerStateChangedPayload$;!function(e){e.inboundSchema=WebhookCustomerStateChangedPayload$inboundSchema,e.outboundSchema=WebhookCustomerStateChangedPayload$outboundSchema}(WebhookCustomerStateChangedPayload$||(WebhookCustomerStateChangedPayload$={}));class WebhookVerificationError extends Error{constructor(e){super(e),this.message=e}}const parseEvent=e=>{try{switch(e.type){case"customer.created":return WebhookCustomerCreatedPayload$inboundSchema.parse(e);case"customer.updated":return WebhookCustomerUpdatedPayload$inboundSchema.parse(e);case"customer.deleted":return WebhookCustomerDeletedPayload$inboundSchema.parse(e);case"customer.state_changed":return WebhookCustomerStateChangedPayload$inboundSchema.parse(e);case"benefit.created":return WebhookBenefitCreatedPayload$inboundSchema.parse(e);case"benefit_grant.created":return WebhookBenefitGrantCreatedPayload$inboundSchema.parse(e);case"benefit_grant.cycled":return WebhookBenefitGrantCycledPayload$inboundSchema.parse(e);case"benefit_grant.revoked":return WebhookBenefitGrantRevokedPayload$inboundSchema.parse(e);case"benefit_grant.updated":return WebhookBenefitGrantUpdatedPayload$inboundSchema.parse(e);case"benefit.updated":return WebhookBenefitUpdatedPayload$inboundSchema.parse(e);case"checkout.created":return WebhookCheckoutCreatedPayload$inboundSchema.parse(e);case"checkout.updated":return WebhookCheckoutUpdatedPayload$inboundSchema.parse(e);case"order.created":return WebhookOrderCreatedPayload$inboundSchema.parse(e);case"order.paid":return WebhookOrderPaidPayload$inboundSchema.parse(e);case"order.updated":return WebhookOrderUpdatedPayload$inboundSchema.parse(e);case"order.refunded":return WebhookOrderRefundedPayload$inboundSchema.parse(e);case"organization.updated":return WebhookOrganizationUpdatedPayload$inboundSchema.parse(e);case"product.created":return WebhookProductCreatedPayload$inboundSchema.parse(e);case"product.updated":return WebhookProductUpdatedPayload$inboundSchema.parse(e);case"refund.created":return WebhookRefundCreatedPayload$inboundSchema.parse(e);case"refund.updated":return WebhookRefundUpdatedPayload$inboundSchema.parse(e);case"subscription.active":return WebhookSubscriptionActivePayload$inboundSchema.parse(e);case"subscription.canceled":return WebhookSubscriptionCanceledPayload$inboundSchema.parse(e);case"subscription.created":return WebhookSubscriptionCreatedPayload$inboundSchema.parse(e);case"subscription.revoked":return WebhookSubscriptionRevokedPayload$inboundSchema.parse(e);case"subscription.uncanceled":return WebhookSubscriptionUncanceledPayload$inboundSchema.parse(e);case"subscription.updated":return WebhookSubscriptionUpdatedPayload$inboundSchema.parse(e);default:throw new SDKValidationError(`Unknown event type: ${e.type}`,e.type,e)}}catch(t){throw new SDKValidationError("Failed to parse event",t,e)}},validateEvent=(e,t,o)=>{const a=Buffer.from(o,"utf-8").toString("base64"),n=new distExports.Webhook(a);try{const o=n.verify(e,t);return parseEvent(o)}catch(e){if(e instanceof distExports.WebhookVerificationError)throw new WebhookVerificationError(e.message);throw e}};var Checkout=({accessToken:e,successUrl:t,server:o,theme:a,includeCheckoutId:n=!0})=>async({url:r})=>{const d=new Polar({accessToken:e,server:o}),i=r.searchParams.getAll("products");if(0===i.length)return Response.json({error:"Missing products in query params"},{status:400});const u=t?new URL(t):void 0;u&&n&&u.searchParams.set("checkoutId","{CHECKOUT_ID}");try{const e=await d.checkouts.create({products:i,successUrl:u?decodeURI(u.toString()):void 0,customerId:r.searchParams.get("customerId")??void 0,externalCustomerId:r.searchParams.get("customerExternalId")??void 0,customerEmail:r.searchParams.get("customerEmail")??void 0,customerName:r.searchParams.get("customerName")??void 0,customerBillingAddress:r.searchParams.has("customerBillingAddress")?JSON.parse(r.searchParams.get("customerBillingAddress")??"{}"):void 0,customerTaxId:r.searchParams.get("customerTaxId")??void 0,customerIpAddress:r.searchParams.get("customerIpAddress")??void 0,customerMetadata:r.searchParams.has("customerMetadata")?JSON.parse(r.searchParams.get("customerMetadata")??"{}"):void 0,allowDiscountCodes:r.searchParams.has("allowDiscountCodes")?"true"===r.searchParams.get("allowDiscountCodes"):void 0,discountId:r.searchParams.get("discountId")??void 0,metadata:r.searchParams.has("metadata")?JSON.parse(r.searchParams.get("metadata")??"{}"):void 0}),t=new URL(e.url);return a&&t.searchParams.set("theme",a),Response.redirect(t.toString())}catch(e){return console.error(e),Response.json({error:"Internal server error"},{status:500})}},Webhooks=({webhookSecret:e,onPayload:t,entitlements:o,...a})=>async({request:n})=>{if("POST"!==n.method)return Response.json({message:"Method not allowed"},{status:405});const r=await n.text(),d={"webhook-id":n.headers.get("webhook-id")??"","webhook-timestamp":n.headers.get("webhook-timestamp")??"","webhook-signature":n.headers.get("webhook-signature")??""};let i;try{i=validateEvent(r,d,e)}catch(e){return console.log(e),e instanceof WebhookVerificationError?Response.json({received:!1},{status:403}):Response.json({error:"Internal server error"},{status:500})}return await handleWebhookPayload(i,{webhookSecret:e,entitlements:o,onPayload:t,...a}),Response.json({received:!0})};export{Checkout as C,Webhooks as W};