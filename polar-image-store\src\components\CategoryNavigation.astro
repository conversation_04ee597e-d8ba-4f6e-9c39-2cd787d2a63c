---
export interface Category {
  id: string;
  name: string;
  count?: number;
}

export interface Props {
  categories: Category[];
  activeCategory?: string;
}

const {
  categories,
  activeCategory = 'all'
} = Astro.props;
---

<section class="py-8 bg-white border-b border-primary-100">
  <div class="container">

    
    <!-- Category Navigation -->
    <!-- Scroll container -->
    <div class="overflow-x-auto scrollbar-hide" id="categoryScroll">
      <div class="flex gap-2 pb-2 min-w-max">
        {categories.map((category) => (
          <button
            class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
              activeCategory === category.id
                ? 'bg-accent-600 text-white shadow-md'
                : 'bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900'
            }`}
            data-category={category.id}
          >
            {category.name}
            {category.count && (
              <span class={`text-xs px-2 py-0.5 rounded-full ${
                activeCategory === category.id
                  ? 'bg-white/20 text-white'
                  : 'bg-primary-200 text-primary-600'
              }`}>
                {category.count}
              </span>
            )}
          </button>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const scrollContainer = document.getElementById('categoryScroll');
    const categoryTabs = document.querySelectorAll('.category-tab');

    if (!scrollContainer) return;

    // Category tab click handlers
    categoryTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const categoryId = e.currentTarget.dataset.category;

        // Remove active class from all tabs
        categoryTabs.forEach(t => {
          t.classList.remove('bg-accent-600', 'text-white', 'shadow-md');
          t.classList.add('bg-primary-50', 'text-primary-700');
        });

        // Add active class to clicked tab
        e.currentTarget.classList.remove('bg-primary-50', 'text-primary-700');
        e.currentTarget.classList.add('bg-accent-600', 'text-white', 'shadow-md');

        // Dispatch custom event for filtering
        console.log('📡 Dispatching categoryChange event:', categoryId);
        window.dispatchEvent(new CustomEvent('categoryChange', {
          detail: { categoryId }
        }));
      });
    });
  });
</script>
