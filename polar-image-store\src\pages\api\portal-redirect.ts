import type { APIRoute } from 'astro';
import { createPolarClient } from '../../utils/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const email = url.searchParams.get('email');
    
    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // If email is provided, try to create pre-authenticated link
    if (email && email.trim()) {
      try {
        // Find customer by email
        const customers = await polar.customers.list({ 
          email: email.trim(),
          organizationId 
        });
        
        const customer = customers.result?.items?.[0];
        
        if (customer?.id) {
          // Create pre-authenticated customer session
          const session = await polar.customerSessions.create({ 
            customerId: customer.id 
          });
          
          if (session.customerPortalUrl) {
            return Response.redirect(session.customerPortalUrl, 302);
          }
        }
      } catch (error) {
        console.error('Error creating customer session:', error);
        // Fall through to direct portal redirect
      }
    }

    // Fallback: Redirect to direct portal URL where customer can enter email
    // Try to get organization info to get the slug
    try {
      const organization = await polar.organizations.get({ id: organizationId });
      const orgSlug = organization.slug;

      if (orgSlug) {
        const directPortalUrl = `https://polar.sh/${orgSlug}/portal`;
        return Response.redirect(directPortalUrl, 302);
      }
    } catch (error) {
      console.error('Error fetching organization info:', error);
    }

    // Final fallback: Use a generic portal URL
    const directPortalUrl = `https://polar.sh/portal`;
    return Response.redirect(directPortalUrl, 302);
    
  } catch (error) {
    console.error('Portal redirect error:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to redirect to customer portal' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
