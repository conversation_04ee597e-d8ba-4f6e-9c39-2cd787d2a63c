# Customer Portal Integration

## Tổng quan

Customer Portal đã được tích hợp thành công vào dự án InfPik, cho phép khách hàng truy cập và quản lý đơn hàng của họ mà không cần hệ thống đăng nhập phức tạp.

## Tính năng

### 🎯 **Kh<PERSON>ch hàng có thể:**
- Xem lịch sử đơn hàng và subscriptions
- Tải xuống file đã mua
- Quản lý license keys (nếu có)
- Xem receipts và refunds
- Cập nhật thông tin thanh toán

### 🔧 **Cách hoạt động:**
1. **Email-based Authentication**: Khách hàng chỉ cần nhập email đã dùng để mua hàng
2. **Pre-authenticated Links**: Hệ thống tự động tạo link đã xác thực cho khách hàng có trong database
3. **Fallback to Direct Portal**: <PERSON>ếu không tìm thấy customer, redirect đến portal chính của Polar

## Implementation

### 📁 **Files đã tạo/cập nhật:**

#### 1. API Route
- **File**: `src/pages/api/portal-redirect.ts`
- **Chức năng**: Handle redirect logic đến Customer Portal
- **Logic**:
  - Nếu có email parameter: tìm customer và tạo pre-authenticated link
  - Nếu không có email: redirect đến direct portal URL
  - Error handling cho các trường hợp edge cases

#### 2. UI Component
- **File**: `src/components/CustomerPortalLink.astro`
- **Variants**:
  - `button`: Simple button link
  - `link`: Text link với icon
  - `card`: Full card với email form (dùng trong success page)

#### 3. Integration Points
- **Header**: "My Orders" button (desktop + mobile)
- **Footer**: "Order History" link
- **Success Page**: Customer Portal card với email form
- **Mobile Menu**: "My Orders" link

### 🔗 **URLs và Endpoints:**

```
/api/portal-redirect                    # Main redirect endpoint
/api/portal-redirect?email=<EMAIL>  # Email-based quick access
```

### 🎨 **UI Components Usage:**

```astro
<!-- Button variant -->
<CustomerPortalLink variant="button" />

<!-- Link variant -->
<CustomerPortalLink variant="link" />

<!-- Card variant với email form -->
<CustomerPortalLink variant="card" showEmailForm={true} />
```

## User Experience

### 🚀 **Customer Journey:**

1. **Từ Success Page**:
   - Sau khi mua hàng → Customer Portal card hiển thị
   - Click "Go to Customer Portal" hoặc nhập email → Redirect đến portal

2. **Từ Header/Footer**:
   - Click "My Orders" hoặc "Order History" → Redirect đến portal
   - Nhập email để xác thực → Truy cập trực tiếp

3. **Tại Customer Portal**:
   - Nhập email → Nhận verification code
   - Truy cập đầy đủ orders, downloads, settings

### 📱 **Responsive Design:**
- Desktop: Header buttons + footer links
- Mobile: Mobile menu + footer links
- Success page: Responsive card layout

## Technical Details

### 🔐 **Security:**
- Sử dụng Polar's Customer Sessions API
- Pre-authenticated links có thời hạn
- Email verification qua Polar
- Không lưu trữ sensitive data

### ⚡ **Performance:**
- API calls được cache
- Error handling robust
- Fallback mechanisms

### 🌐 **Environment Variables:**
```env
POLAR_ACCESS_TOKEN=polar_pat_xxx
POLAR_ORGANIZATION_ID=org_xxx
```

## Testing

### ✅ **Đã test thành công:**
1. Header "My Orders" button → Redirect đến portal ✓
2. Footer "Order History" link → Redirect đến portal ✓
3. Success page Customer Portal card → Hiển thị đúng ✓
4. Email form trong success page → Redirect với email parameter ✓
5. Mobile menu integration → Hoạt động tốt ✓

### 🔍 **Test URLs:**
- `http://localhost:4321/` - Homepage với header links
- `http://localhost:4321/success` - Success page với portal card
- `http://localhost:4321/api/portal-redirect` - Direct portal access
- `http://localhost:4321/api/portal-redirect?email=<EMAIL>` - Email-based access

## Maintenance

### 📊 **Monitoring:**
- Check API response times
- Monitor redirect success rates
- Track customer portal usage

### 🔄 **Updates:**
- Component styles có thể customize trong CSS
- API logic có thể extend thêm features
- Error messages có thể localize

## Next Steps

### 🚀 **Potential Enhancements:**
1. **Analytics**: Track portal usage và customer behavior
2. **Personalization**: Customize portal experience based on purchase history
3. **Notifications**: Email notifications với portal links
4. **Mobile App**: Deep linking đến portal
5. **Multi-language**: Support multiple languages

### 🎯 **Business Benefits:**
- Giảm customer support workload
- Tăng customer satisfaction
- Professional brand experience
- Automated customer management
