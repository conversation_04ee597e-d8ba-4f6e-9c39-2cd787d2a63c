globalThis.process??={},globalThis.process.env??={};import{d as defineMiddleware,s as sequence}from"./chunks/index_kkHkWl4A.mjs";import{getOptimalFormat}from"./chunks/imageOptimization_Dac7PwDt.mjs";import"./chunks/astro-designed-error-pages_BAUzKiqr.mjs";import"./chunks/astro/server_BgKLHZ62.mjs";const onRequest$2=defineMiddleware((async(e,t)=>{const{request:s,url:a}=e;if(a.pathname.startsWith("/cdn-cgi/image/")){const e=s.headers.get("user-agent")||"",t=s.headers.get("accept")||"";let i;i=t.includes("image/avif")?"avif":t.includes("image/webp")?"webp":getOptimalFormat(e);const r=a.pathname.split("/");if(r.length>=4){const e=r[3],t=r.slice(4).join("/");if(e.includes("format=auto")){const s=e.replace("format=auto",`format=${i}`),r=new URL(`/cdn-cgi/image/${s}/${t}`,a.origin);return Response.redirect(r.toString(),302)}}}const i=await t();return a.pathname.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/i)&&(i.headers.set("Cache-Control","public, max-age=31536000, immutable"),i.headers.set("Vary","Accept")),a.pathname.startsWith("/cdn-cgi/image/")&&(i.headers.set("Vary","Accept"),i.headers.set("Accept-CH","Viewport-Width, Width, DPR")),i})),onRequest$1=(e,t)=>(e.isPrerendered&&(e.locals.runtime??={env:process.env}),t()),onRequest=sequence(onRequest$1,onRequest$2);export{onRequest};