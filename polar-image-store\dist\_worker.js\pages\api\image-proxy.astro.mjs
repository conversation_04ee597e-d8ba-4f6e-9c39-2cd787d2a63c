globalThis.process??={},globalThis.process.env??={};import{getOptimalFormat,getOptimizedImageUrl}from"../../chunks/imageOptimization_Dac7PwDt.mjs";export{renderers}from"../../renderers.mjs";const GET=async({request:e,url:t})=>{try{const r=t.searchParams,s=r.get("url"),a=r.get("width"),o=r.get("height"),n=r.get("quality"),i=r.get("format"),g=r.get("fit");if(!s)return new Response("Missing image URL",{status:400});const p=e.headers.get("user-agent")||"",l=e.headers.get("accept")||"";let c;c=i&&"auto"!==i?i:l.includes("image/avif")?"avif":l.includes("image/webp")?"webp":getOptimalFormat(p);const m={format:c,fit:g||"scale-down"};a&&(m.width=parseInt(a)),o&&(m.height=parseInt(o)),n&&(m.quality=parseInt(n));const u=getOptimizedImageUrl(s,m);if(u===s||s.startsWith("/"))return Response.redirect(s,302);const d=`${new URL(e.url).origin}${u}`;return Response.redirect(d,302)}catch(e){return console.error("Image proxy error:",e),new Response("Internal server error",{status:500})}},POST=async()=>new Response("Method not allowed",{status:405}),_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,POST:POST},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};