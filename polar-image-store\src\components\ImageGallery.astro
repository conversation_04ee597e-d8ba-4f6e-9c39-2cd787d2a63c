---
import OptimizedImage from './OptimizedImage.astro';

export interface Props {
  images: string[];
  productName: string;
}

const { images, productName } = Astro.props;

// If no images, use placeholder
const displayImages = images.length > 0 ? images : ['/placeholder-image.svg'];
---

<div class="flex flex-col gap-4">
  <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group">
    <OptimizedImage
      id="mainImage"
      src={displayImages[0]}
      alt={productName}
      preset="productDetail"
      loading="eager"
      fetchpriority="high"
      data-lightbox-index="0"
      class="max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105"
    />
  </div>

  {displayImages.length > 1 && (
    <div class="flex gap-4 overflow-x-auto pb-2 mt-6">
      {displayImages.slice(1).map((image, index) => (
        <div class="flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:border-primary-500 hover:scale-105 border-gray-200" data-thumbnail-index={index + 1} data-image-src={image} data-lightbox-index={index + 1}>
          <OptimizedImage
            src={image}
            alt={`${productName} - Image ${index + 2}`}
            preset="thumbnail"
            loading="eager"
            class="w-full h-full object-cover"
          />
        </div>
      ))}
    </div>
  )}
</div>

<!-- Lightbox Modal -->
<div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in">
  <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto pt-20">
    <button
      class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors"
      onclick="closeLightbox()"
    >
      &times;
    </button>
    <button
      class="absolute top-1/2 -translate-y-1/2 -left-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80"
      onclick="prevImage()"
    >
      &#8249;
    </button>
    <OptimizedImage
      id="lightboxImage"
      src="/placeholder-image.svg"
      alt="Lightbox image"
      width={1200}
      height={800}
      loading="lazy"
      class="max-w-full max-h-full object-contain rounded-lg"
    />
    <button
      class="absolute top-1/2 -translate-y-1/2 -right-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80"
      onclick="nextImage()"
    >
      &#8250;
    </button>
    <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full">
      <span id="imageCounter">1 / 1</span>
    </div>
  </div>
</div>

<style>
  /* Custom animations and mobile responsive adjustments */
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease;
  }

  #lightbox.active {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }

  @media (max-width: 768px) {
    #lightbox .absolute.-left-20 {
      left: 10px;
      font-size: 2rem;
      padding: 0.5rem;
    }

    #lightbox .absolute.-right-20 {
      right: 10px;
      font-size: 2rem;
      padding: 0.5rem;
    }

    #lightbox .absolute.-top-12 {
      top: 10px;
      right: 10px;
      font-size: 1.5rem;
    }

    #lightbox .absolute.-bottom-12 {
      bottom: 10px;
    }
  }
</style>

<script>
  // Global variables for lightbox
  let currentImageIndex = 0;
  let galleryImages = [];

  // Initialize gallery
  document.addEventListener('DOMContentLoaded', function() {
    // Get all images from the gallery (main image + thumbnails)
    const mainImg = document.getElementById('mainImage');
    const thumbnailImgs = document.querySelectorAll('[data-thumbnail-index]');

    galleryImages = [];
    if (mainImg) {
      galleryImages.push(mainImg.src);
    }

    thumbnailImgs.forEach(thumb => {
      const img = thumb.querySelector('img');
      if (img) {
        galleryImages.push(img.src);
      }
    });

    // Add event listeners for main image
    if (mainImg) {
      mainImg.addEventListener('click', function() {
        const lightboxIndex = parseInt(this.getAttribute('data-lightbox-index')) || 0;
        openLightbox(lightboxIndex);
      });
    }

    // Add event listeners for thumbnails
    thumbnailImgs.forEach(thumb => {
      thumb.addEventListener('click', function() {
        const lightboxIndex = parseInt(this.getAttribute('data-lightbox-index'));

        if (lightboxIndex !== null) {
          openLightbox(lightboxIndex);
        }
      });
    });
  });

  // Change main image
  function changeMainImage(imageSrc, index) {
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
      mainImage.src = imageSrc;
    }

    // Update active thumbnail border
    document.querySelectorAll('[data-thumbnail-index]').forEach((thumb) => {
      const thumbIndex = parseInt(thumb.getAttribute('data-thumbnail-index'));
      if (thumbIndex === index) {
        thumb.classList.remove('border-gray-200');
        thumb.classList.add('border-primary-500');
      } else {
        thumb.classList.remove('border-primary-500');
        thumb.classList.add('border-gray-200');
      }
    });

    currentImageIndex = index;
  }

  // Open lightbox
  function openLightbox(index) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const counter = document.getElementById('imageCounter');
    
    if (lightbox && lightboxImage && galleryImages.length > 0) {
      currentImageIndex = index;
      lightboxImage.src = galleryImages[currentImageIndex];
      lightboxImage.alt = `Image ${currentImageIndex + 1}`;
      counter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
      lightbox.classList.add('active');
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }
  }

  // Close lightbox
  function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    if (lightbox) {
      lightbox.classList.remove('active');
      document.body.style.overflow = 'auto';
    }
  }

  // Previous image
  function prevImage() {
    if (galleryImages.length > 1) {
      currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
      updateLightboxImage();
    }
  }

  // Next image
  function nextImage() {
    if (galleryImages.length > 1) {
      currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
      updateLightboxImage();
    }
  }

  // Update lightbox image
  function updateLightboxImage() {
    const lightboxImage = document.getElementById('lightboxImage');
    const counter = document.getElementById('imageCounter');
    
    if (lightboxImage && counter) {
      lightboxImage.src = galleryImages[currentImageIndex];
      lightboxImage.alt = `Image ${currentImageIndex + 1}`;
      counter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
    }
  }

  // Keyboard navigation
  document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.classList.contains('active')) {
      switch(e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          prevImage();
          break;
        case 'ArrowRight':
          nextImage();
          break;
      }
    }
  });

  // Close lightbox when clicking outside image
  document.addEventListener('click', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (e.target === lightbox) {
      closeLightbox();
    }
  });
</script>
